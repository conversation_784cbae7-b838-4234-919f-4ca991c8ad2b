<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="CarConDB" connectionString="Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\cars_DB.mdf;Integrated Security=True;User Instance=True" providerName="System.Data.SqlClient" />
  </connectionStrings>

  <appSettings>
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>

  <system.web>
    <compilation debug="true" targetFramework="4.8" />
    <httpRuntime targetFramework="4.8" maxRequestLength="51200" executionTimeout="3600" />
    <pages enableViewState="true" validateRequest="false">
      <controls>
        <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
      </controls>
    </pages>
    <customErrors mode="Off" />
    <trust level="Full" />
  </system.web>

  <system.webServer>
    <defaultDocument>
      <files>
        <clear />
        <add value="Default.aspx" />
      </files>
    </defaultDocument>
    <directoryBrowse enabled="false" />
  </system.webServer>
</configuration>
