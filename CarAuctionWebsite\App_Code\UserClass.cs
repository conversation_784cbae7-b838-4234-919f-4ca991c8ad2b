using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class UserClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public DataTable Login(string userName)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_user WHERE User_name=@User_name", con);
            cmd.Parameters.AddWithValue("@User_name", userName);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public void AddUser(string userName, string password)
        {
            SqlCommand cmd = new SqlCommand("INSERT INTO TB_user (User_name, User_password) VALUES (@User_name, @User_password)", con);
            cmd.Parameters.AddWithValue("@User_name", userName);
            cmd.Parameters.AddWithValue("@User_password", password);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public void UpdateUser(string userName, string password)
        {
            SqlCommand cmd = new SqlCommand("UPDATE TB_user SET User_password=@User_password WHERE User_name=@User_name", con);
            cmd.Parameters.AddWithValue("@User_name", userName);
            cmd.Parameters.AddWithValue("@User_password", password);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public void DeleteUser(string userName)
        {
            SqlCommand cmd = new SqlCommand("DELETE FROM TB_user WHERE User_name=@User_name", con);
            cmd.Parameters.AddWithValue("@User_name", userName);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }
    }
}
