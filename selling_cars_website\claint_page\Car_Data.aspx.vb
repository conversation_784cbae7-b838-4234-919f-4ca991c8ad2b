﻿
Partial Class claint_page_Car_Data
    Inherits System.Web.UI.Page
    Dim obj_car As New Car_Class
    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If TextBox_travel.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال المسافة المقطوعه"
            Exit Sub
            TextBox_travel.Focus()
        End If
        If TextBox_damages.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال الاضرار"
            TextBox_damages.Focus()
            Exit Sub
        End If
        If TextBox_Cylinders.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال عدد الاسطوانات"
            TextBox_Cylinders.Focus()
            Exit Sub
        End If
        If TextBox_color.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال لون السيارة"
            TextBox_color.Focus()
            Exit Sub
        End If
        If TextBox_Engine.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال نوع المحرك "
            TextBox_Engine.Focus()
            Exit Sub
        End If
        If TextBox_type.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال نوع السياره"
            TextBox_type.Focus()
            Exit Sub
        End If
        If TextBox_year.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال الموديل"
            TextBox_year.Focus()
            Exit Sub
        End If
        If TextBox_keys.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال عدد المفاتيح"
            TextBox_keys.Focus()
            Exit Sub
        End If
        If TextBox_location.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال موقع السياره"
            TextBox_location.Focus()
            Exit Sub
        End If
       
        If TextBox_note.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال الملاحظات"
            TextBox_note.Focus()
            Exit Sub
        End If

        If TextBox_value_ac.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال قيمة بداية المزاد"
            TextBox_value_ac.Focus()
            Exit Sub
        End If
        If TextBox_buy.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال قيمة الشراء الفوري"
            TextBox_buy.Focus()
            Exit Sub
        End If
        If DropDownList_gas.Text = "اختر نوع الوقود" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إختيار نوع الوقود"
            TextBox_buy.Focus()
            Exit Sub
        End If



        obj_car.add_car(TextBox_travel.Text, TextBox_damages.Text, TextBox_Cylinders.Text, TextBox_color.Text, TextBox_Engine.Text, TextBox_type.Text, TextBox_year.Text, DropDownList_gas.Text, TextBox_keys.Text, TextBox_location.Text, TextBox_note.Text, Session("customer_phone"), TextBox_value_ac.Text, TextBox_buy.Text)
        Label1.Visible = True
        Label1.ForeColor = Drawing.Color.Green
        Label1.Text = "تم نشر بيانات المركبة بنجاح"
        GridView1.DataBind()

    End Sub
End Class
