-- Sample Data for Car Auction Website
-- بيانات تجريبية لموقع مزاد السيارات

-- إدراج مستخدمين إداريين
INSERT INTO TB_user (User_name, User_password) VALUES 
('admin', '123456'),
('manager', 'password123');

-- إدراج عملاء تجريبيين
INSERT INTO TB_customer (phone, city, region, address, near_point, customer_name, bank_name, branch, customer_type, password) VALUES 
('**********', 'طرابلس', 'المركز', 'شارع الجمهورية', 'الساحة الخضراء', 'أحمد محمد علي', 'البنك الأهلي', 'الفرع الرئيسي', 'حساب شخصي', '123456'),
('**********', 'بنغازي', 'الفويهات', 'شارع جمال عبد الناصر', 'مستشفى الجلاء', 'فاطمة أحمد', 'بنك الجمهورية', 'فرع بنغازي', 'حساب شخصي', '123456'),
('**********', 'مصراتة', 'وسط المدينة', 'شارع طرابلس', 'السوق المركزي', 'محمد عبد الله', 'البنك التجاري', 'فرع مصراتة', 'حساب شركة', '123456'),
('**********', 'الزاوية', 'المدينة القديمة', 'شارع الشهداء', 'مسجد الزاوية', 'عائشة سالم', 'بنك الوحدة', 'فرع الزاوية', 'حساب شخصي', '123456');

-- إدراج سيارات تجريبية
INSERT INTO TB_car (distance_travele, damages, Cylinders, color, Engine_Type, car_type, year_made, gas_type, keys, location, notes, state_pay, add_by, start_value, buy_now) VALUES 
('50000', 'لا توجد أضرار', '4', 'أبيض', 'بنزين', 'تويوتا كورولا', '2020', 'بنزين', 'مفتاحين', 'طرابلس', 'سيارة في حالة ممتازة', 0, 'admin', 25000, 30000),
('75000', 'خدوش بسيطة', '6', 'أسود', 'بنزين', 'نيسان التيما', '2019', 'بنزين', 'مفتاح واحد', 'بنغازي', 'سيارة نظيفة مع صيانة دورية', 0, 'admin', 22000, 27000),
('120000', 'تحتاج صيانة بسيطة', '4', 'أحمر', 'بنزين', 'هيونداي إلنترا', '2018', 'بنزين', 'مفتاحين', 'مصراتة', 'سيارة اقتصادية', 0, 'admin', 18000, 22000),
('30000', 'لا توجد أضرار', '8', 'أزرق', 'بنزين', 'شيفروليه كامارو', '2021', 'بنزين', 'مفتاحين', 'طرابلس', 'سيارة رياضية فاخرة', 0, 'admin', 45000, 55000),
('60000', 'خدوش في الباب الأمامي', '4', 'فضي', 'بنزين', 'كيا سيراتو', '2019', 'بنزين', 'مفتاحين', 'الزاوية', 'سيارة عملية واقتصادية', 0, 'admin', 20000, 24000);

-- إدراج صور السيارات (أسماء الصور الموجودة)
INSERT INTO TB_Photo (Photo_name, id, state) VALUES 
('photo_2024-04-17_21-31-23.jpg', 1, 1),
('photo_2024-04-17_21-31-26.jpg', 2, 1),
('photo_2024-04-17_21-31-28.jpg', 3, 1),
('photo_2024-04-17_21-31-32.jpg', 4, 1),
('photo_2024-04-17_21-31-59.jpg', 5, 1),
('photo_2024-04-17_21-32-40.jpg', 1, 0),
('photo_2024-04-17_21-41-00.jpg', 2, 0),
('photo_2024-04-17_21-41-03.jpg', 3, 0);

-- إدراج مزادات تجريبية
INSERT INTO TB_Auction (id, start_date, end_date, start_time, end_time, penal_clause) VALUES 
(1, '2024-01-15', '2024-01-20', '10:00:00', '18:00:00', 1000),
(2, '2024-01-16', '2024-01-21', '09:00:00', '17:00:00', 1200),
(3, '2024-01-17', '2024-01-22', '11:00:00', '19:00:00', 800),
(4, '2024-01-18', '2024-01-23', '10:30:00', '18:30:00', 2000),
(5, '2024-01-19', '2024-01-24', '09:30:00', '17:30:00', 900);

-- إدراج مدفوعات تجريبية
INSERT INTO TB_payment (phone, auction_id, max_value, sub_date, sub_time, pay_state, pay_way, shipping_way) VALUES 
('**********', 1, 26000, '2024-01-19', '15:30:00', 0, '', ''),
('**********', 1, 25500, '2024-01-19', '14:20:00', 0, '', ''),
('**********', 2, 23000, '2024-01-20', '16:45:00', 0, '', ''),
('**********', 3, 19000, '2024-01-21', '13:15:00', 0, '', ''),
('**********', 4, 47000, '2024-01-22', '17:00:00', 0, '', '');

-- إدراج بيانات بنوك تجريبية
INSERT INTO TB_bank (account_id, totail) VALUES 
('ACC001', 50000.00),
('ACC002', 75000.00),
('ACC003', 100000.00),
('ACC004', 60000.00),
('ACC005', 80000.00);

-- تحديث بعض السيارات كمباعة للاختبار
UPDATE TB_car SET state_pay = 1 WHERE id IN (1, 3);

-- تحديث بعض المدفوعات كمكتملة
UPDATE TB_payment SET pay_state = 1, pay_way = 'تحويل بنكي', shipping_way = 'استلام من المعرض' 
WHERE auction_id IN (1, 3);

-- إدراج المزيد من الصور للسيارات
INSERT INTO TB_Photo (Photo_name, id, state) VALUES 
('photo_2024-04-17_21-44-51.jpg', 4, 1),
('photo_2024-04-17_21-44-58.jpg', 5, 1),
('photo_2024-04-17_21-45-00.jpg', 1, 0),
('photo_2024-04-17_21-45-12.jpg', 2, 0),
('photo_2024-04-17_21-45-17.jpg', 3, 0),
('photo_2024-04-17_21-45-20.jpg', 4, 0),
('photo_2024-04-17_21-55-57.jpg', 5, 0);

-- إضافة المزيد من العملاء
INSERT INTO TB_customer (phone, city, region, address, near_point, customer_name, bank_name, branch, customer_type, password) VALUES 
('**********', 'سبها', 'المدينة', 'شارع الفاتح', 'المطار', 'خالد إبراهيم', 'البنك الأهلي', 'فرع سبها', 'حساب شخصي', '123456'),
('**********', 'غريان', 'الوسط', 'شارع الاستقلال', 'البلدية', 'نورا محمد', 'بنك الجمهورية', 'فرع غريان', 'حساب شخصي', '123456'),
('**********', 'زليتن', 'المركز', 'شارع الجامعة', 'الجامعة', 'سامي أحمد', 'البنك التجاري', 'فرع زليتن', 'حساب شركة', '123456');

-- إضافة المزيد من السيارات
INSERT INTO TB_car (distance_travele, damages, Cylinders, color, Engine_Type, car_type, year_made, gas_type, keys, location, notes, state_pay, add_by, start_value, buy_now) VALUES 
('40000', 'حالة ممتازة', '4', 'أبيض', 'بنزين', 'هوندا سيفيك', '2020', 'بنزين', 'مفتاحين', 'سبها', 'سيارة موثوقة واقتصادية', 0, 'admin', 24000, 29000),
('85000', 'خدوش بسيطة في المؤخرة', '6', 'رمادي', 'بنزين', 'فورد فوكس', '2018', 'بنزين', 'مفتاح واحد', 'غريان', 'سيارة أوروبية بحالة جيدة', 0, 'admin', 19000, 23000),
('25000', 'كالجديدة', '4', 'أزرق', 'هايبرد', 'تويوتا بريوس', '2021', 'هايبرد', 'مفتاحين', 'زليتن', 'سيارة صديقة للبيئة', 0, 'admin', 28000, 33000);

-- إضافة صور للسيارات الجديدة
INSERT INTO TB_Photo (Photo_name, id, state) VALUES 
('photo_2024-04-18_17-47-09.jpg', 6, 1),
('photo_2024-04-18_17-47-12.jpg', 7, 1),
('photo_2024-04-18_17-47-16.jpg', 8, 1);

PRINT 'تم إدراج البيانات التجريبية بنجاح!'
PRINT 'Sample data inserted successfully!'
