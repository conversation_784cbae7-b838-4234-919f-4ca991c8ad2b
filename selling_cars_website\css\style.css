/******************************************************************
  Template Name: HVAC
  Description: HVAC Car Dealer HTML Template
  Author: Colorlib
  Author URI: https://www.colorlib.com
  Version: 1.0
  Created: Colorlib
******************************************************************/

/*------------------------------------------------------------------
[Table of contents]

1.  Template default CSS
	1.1	Variables
	1.2	Mixins
	1.3	Flexbox
	1.4	Reset
2.  Helper Css
3.  Header Section
4.  Hero Section
5.  Service Section
6.  Car Section
7.  Feature Section
8.  Latest Blog Section
9.  Contact
10.  Footer Style
-------------------------------------------------------------------*/

/*----------------------------------------*/

/* Template default CSS
/*----------------------------------------*/

html,
body {
	height: 100%;
	font-family: "Lato", sans-serif;
	-webkit-font-smoothing: antialiased;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0;
	color: #111111;
	font-weight: 400;
	font-family: "Lato", sans-serif;
}

h1 {
	font-size: 70px;
}

h2 {
	font-size: 36px;
}

h3 {
	font-size: 30px;
}

h4 {
	font-size: 24px;
}

h5 {
	font-size: 18px;
}

h6 {
	font-size: 16px;
}

p {
	font-size: 15px;
	font-family: "Lato", sans-serif;
	color: #727171;
	font-weight: 400;
	line-height: 25px;
	margin: 0 0 15px 0;
}

img {
	max-width: 100%;
}

input:focus,
select:focus,
button:focus,
textarea:focus {
	outline: none;
}

a:hover,
a:focus {
	text-decoration: none;
	outline: none;
	color: #ffffff;
}

ul,
ol {
	padding: 0;
	margin: 0;
}

/*---------------------
  Helper CSS
-----------------------*/

.section-title {
	margin-bottom: 75px;
	text-align: center;
}

.section-title span {
	font-size: 15px;
	color: #db2d2e;
	font-weight: 700;
}

.section-title h2 {
	color: #353535;
	font-size: 40px;
	font-weight: 700;
	margin-top: 5px;
	margin-bottom: 16px;
}

.section-title P {
	margin-bottom: 0;
	font-size: 17px;
	color: #727171;
}

.set-bg {
	background-repeat: no-repeat;
	background-size: cover;
	background-position: top center;
}

.spad {
	padding-top: 100px;
	padding-bottom: 100px;
}

.text-white h1,
.text-white h2,
.text-white h3,
.text-white h4,
.text-white h5,
.text-white h6,
.text-white p,
.text-white span,
.text-white li,
.text-white a {
	color: #fff;
}

/* buttons */

.primary-btn {
	display: inline-block;
	font-size: 15px;
	padding: 12px 24px;
	color: #ffffff;
	font-weight: 700;
	background: #db2d2e;
	border-radius: 2px;
}

.site-btn {
	font-size: 15px;
	color: #ffffff;
	font-weight: 700;
	display: inline-block;
	padding: 15px 35px 12px 38px;
	background: #db2d2e;
	border: none;
	border-radius: 2px;
}

/* Preloder */

#preloder {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 999999;
	background: #ffffff;
}

.loader {
	width: 40px;
	height: 40px;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -13px;
	margin-left: -13px;
	border-radius: 60px;
	animation: loader 0.8s linear infinite;
	-webkit-animation: loader 0.8s linear infinite;
}

@keyframes loader {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
		border: 4px solid #f44336;
		border-left-color: transparent;
	}
	50% {
		-webkit-transform: rotate(180deg);
		transform: rotate(180deg);
		border: 4px solid #673ab7;
		border-left-color: transparent;
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
		border: 4px solid #f44336;
		border-left-color: transparent;
	}
}

@-webkit-keyframes loader {
	0% {
		-webkit-transform: rotate(0deg);
		border: 4px solid #f44336;
		border-left-color: transparent;
	}
	50% {
		-webkit-transform: rotate(180deg);
		border: 4px solid #673ab7;
		border-left-color: transparent;
	}
	100% {
		-webkit-transform: rotate(360deg);
		border: 4px solid #f44336;
		border-left-color: transparent;
	}
}

.spacial-controls {
	position: fixed;
	width: 111px;
	height: 91px;
	top: 0;
	right: 0;
	z-index: 999;
}

.spacial-controls .search-switch {
	display: block;
	height: 100%;
	padding-top: 30px;
	background: #323232;
	text-align: center;
	cursor: pointer;
}

.search-model {
	display: none;
	position: fixed;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	background: #ffffff;
	z-index: 99999;
}

.search-model-form {
	padding: 0 15px;
}

.search-model-form input {
	width: 470px;
	font-size: 40px;
	border: none;
	border-bottom: 2px solid #ededed;
	background: 0 0;
	color: #999;
}

.search-close-switch {
	position: absolute;
	width: 50px;
	height: 50px;
	background: #333;
	color: #fff;
	text-align: center;
	border-radius: 50%;
	font-size: 28px;
	line-height: 28px;
	top: 30px;
	cursor: pointer;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

/*---------------------
  Header
-----------------------*/

.header__top {
	background: #323232;
}

.header__top__widget {
	padding: 12px 0 14px;
}

.header__top__widget li {
	font-size: 15px;
	color: #ffffff;
	display: inline-block;
	margin-right: 35px;
	list-style: none;
}

.header__top__widget li:last-child {
	margin-right: 0;
}

.header__top__widget li i {
	font-size: 18px;
	color: #8d8d8d;
	margin-right: 6px;
}

.header__top__right {
	text-align: right;
}

.header__top__phone {
	display: inline-block;
	margin-right: 20px;
	padding: 14px 0 12px;
}

.header__top__phone i {
	color: #8d8d8d;
	margin-right: 6px;
	font-size: 15px;
}

.header__top__phone span {
	font-size: 15px;
	color: #ffffff;
}

.header__top__social {
	display: inline-block;
	padding: 14px 0 12px;
}

.header__top__social a {
	display: inline-block;
	font-size: 15px;
	color: #ffffff;
	margin-right: 16px;
}

.header__top__social a:last-child {
	margin-right: 0;
}

.header__nav {
	text-align: right;
	padding: 32px 0 33px;
}

.header__logo {
	padding: 35px 0;
}

.header__logo a {
	display: inline-block;
}

.header__menu {
	display: inline-block;
}

.header__menu ul li {
	list-style: none;
	display: inline-block;
	margin-right: 60px;
	position: relative;
}

.header__menu ul li.active a:after {
	width: 100%;
	opacity: 1;
}

.header__menu ul li:hover a:after {
	width: 100%;
	opacity: 1;
}

.header__menu ul li:hover .dropdown {
	top: 32px;
	opacity: 1;
	visibility: visible;
}

.header__menu ul li .dropdown {
	position: absolute;
	left: 0;
	top: 56px;
	width: 180px;
	background: #db2d2e;
	text-align: left;
	padding: 2px 0;
	z-index: 9;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all, 0.5s;
	-o-transition: all, 0.5s;
	transition: all, 0.5s;
}

.header__menu ul li .dropdown li {
	display: block;
	margin-right: 0;
}

.header__menu ul li .dropdown li a {
	font-size: 14px;
	color: #ffffff;
	font-weight: 400;
	padding: 8px 20px;
	text-transform: capitalize;
}

.header__menu ul li .dropdown li a:after {
	display: none;
}

.header__menu ul li a {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
	display: block;
	padding: 5px 0;
	text-transform: uppercase;
	position: relative;
}

.header__menu ul li a:after {
	position: absolute;
	left: 0;
	bottom: 0;
	height: 2px;
	width: 0%;
	background: #db2d2e;
	content: "";
	opacity: 0;
	-webkit-transition: all, 0.5s;
	-o-transition: all, 0.5s;
	transition: all, 0.5s;
}

.header__nav__widget {
	display: inline-block;
}

.header__nav__widget .header__nav__widget__btn {
	display: inline-block;
}

.header__nav__widget .header__nav__widget__btn a {
	font-size: 15px;
	color: #353535;
	margin-right: 20px;
	display: inline-block;
}

.offcanvas-menu-wrapper {
	display: none;
}

.canvas__open {
	display: none;
}

/*---------------------
  Hero
-----------------------*/

.hero {
	padding: 140px 0;
}

.hero__text {
	padding-top: 110px;
}

.hero__text .primary-btn {
	margin-right: 16px;
}

.hero__text .primary-btn img {
	position: relative;
	top: -2px;
}

.hero__text .primary-btn.more-btn {
	background: transparent;
	border: 1px solid #ffffff;
}

.hero__text__title {
	margin-bottom: 28px;
}

.hero__text__title span {
	font-size: 20px;
	color: #ffffff;
	font-weight: 700;
	text-transform: uppercase;
}

.hero__text__title h2 {
	font-size: 60px;
	color: #ffffff;
	font-weight: 700;
	margin-top: 10px;
}

.hero__text__price {
	position: relative;
	padding-left: 140px;
	margin-bottom: 55px;
}

.hero__text__price .car-model {
	font-size: 13px;
	color: #323232;
	font-weight: 700;
	display: inline-block;
	padding: 6px 14px;
	background: #ffffff;
	border-radius: 2px;
	position: absolute;
	left: 0;
	top: 0;
}

.hero__text__price h2 {
	font-size: 50px;
	color: #ffffff;
	font-weight: 700;
	line-height: 45px;
}

.hero__text__price h2 span {
	font-size: 40px;
}

.hero__tab .nav-tabs {
	border-bottom: none;
}

.hero__tab .nav-tabs .nav-item {
	margin-bottom: 5px;
	margin-right: 5px;
}

.hero__tab .nav-tabs .nav-item .nav-link {
	position: relative;
	font-size: 15px;
	font-weight: 700;
	color: #323232;
	display: inline-block;
	padding: 7px 20px;
	border: none;
	border-radius: 2px;
	background: #ffffff;
}

.hero__tab .nav-tabs .nav-item .nav-link:after {
	position: absolute;
	content: "";
	width: 100%;
	height: 10px;
	left: 0;
	bottom: -7px;
	opacity: 0;
	background: #ffffff;
	-webkit-transition: 0.3s;
	-o-transition: 0.3s;
	transition: 0.3s;
}

.hero__tab .nav-tabs .nav-item .nav-link.active:after {
	opacity: 1;
	bottom: -7px;
}

.hero__tab__form {
	background: #ffffff;
	padding: 42px 40px 50px;
	border-radius: 2px;
}

.hero__tab__form h2 {
	color: #323232;
	font-weight: 700;
	margin-bottom: 22px;
}

.hero__tab__form form .select-list {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-right: -20px;
}

.hero__tab__form form .select-list .select-list-item {
	max-width: calc(50% - 20px);
	float: left;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 50%;
	flex: 0 0 50%;
	margin-right: 20px;
	margin-bottom: 20px;
}

.hero__tab__form form .select-list .select-list-item p {
	color: #323232;
	margin-bottom: 5px;
}

.hero__tab__form form .select-list .select-list-item .nice-select {
	width: 100%;
	height: 40px;
	border: 1px solid #ebebeb;
	border-radius: 2px;
}

.hero__tab__form form .select-list .select-list-item .nice-select .list {
	width: 100%;
	border-radius: 0;
	margin-top: 0;
}

.hero__tab__form form .select-list .select-list-item .nice-select.open span {
	display: block !important;
}

.hero__tab__form form .select-list .select-list-item .nice-select:after {
	display: none;
}

.hero__tab__form form .car-price {
	margin-bottom: 45px;
}

.hero__tab__form form .car-price p {
	color: #323232;
	margin-bottom: 5px;
	margin-bottom: 12px;
}

.hero__tab__form form .car-price .price-range-wrap {
	position: relative;
}

.hero__tab__form form .car-price .price-range-wrap .ui-widget-content {
	height: 5px;
	background: rgba(45, 45, 45, 0.1);
	border: none;
	border-radius: 1px;
}

.hero__tab__form form .car-price .price-range-wrap .ui-slider-horizontal .ui-slider-handle {
	top: -5px;
	margin-left: -4px;
}

.hero__tab__form form .car-price .price-range-wrap .ui-slider .ui-slider-handle {
	width: 14px;
	height: 14px;
	background: #ffffff;
	border-radius: 50%;
	cursor: pointer;
}

.hero__tab__form form .car-price .price-range-wrap .ui-state-default,
.hero__tab__form form .car-price .price-range-wrap .ui-widget-content .ui-state-default,
.hero__tab__form form .car-price .price-range-wrap .ui-widget-header .ui-state-default,
.hero__tab__form form .car-price .price-range-wrap .ui-button,
.hero__tab__form form .car-price .price-range-wrap html .ui-button.ui-state-disabled:hover,
.hero__tab__form form .car-price .price-range-wrap html .ui-button.ui-state-disabled:active {
	border: 1.5px solid #db2d2e;
}

.hero__tab__form form .car-price .price-range-wrap .ui-slider .ui-slider-range {
	background: #db2d2e;
}

.hero__tab__form form .car-price .price-input {
	position: absolute;
	left: 89px;
	top: -36px;
}

.hero__tab__form form .car-price .price-input input {
	font-size: 15px;
	color: #323232;
	font-weight: 700;
	border: none;
	width: 180px;
}

/*---------------------
  Feature
-----------------------*/

.feature {
	background: #f7f7f7;
	position: relative;
	z-index: 1;
	padding-bottom: 55px;
}

.feature:after {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 605px;
	height: 404px;
	background-image: url(../img/feature/car.png);
	content: "";
	-webkit-transform: translate(-300px, -202px);
	-ms-transform: translate(-300px, -202px);
	transform: translate(-300px, -202px);
	z-index: -1;
}

.feature__text .section-title {
	text-align: left;
	margin-bottom: 34px;
}

.feature__text .section-title h2 {
	margin-bottom: 0;
}

.feature__text__desc {
	margin-bottom: 35px;
}

.feature__text__desc p {
	margin-bottom: 30px;
}

.feature__text__btn .primary-btn {
	margin-right: 16px;
}

.feature__text__btn .partner-btn {
	background: #222;
}

.feature__item {
	text-align: center;
	float: right;
	margin-bottom: 45px;
}

.feature__item .feature__item__icon {
	height: 100px;
	width: 100px;
	background: #ffffff;
	border-radius: 50%;
	line-height: 100px;
	text-align: center;
	margin-bottom: 20px !important;
	margin: 0 auto;
}

.feature__item h6 {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
}

/*---------------------
  Choose Us
-----------------------*/

.chooseus {
	background: #f7f7f7;
	position: relative;
	height: 530px;
	padding-bottom: 85px;
}

.chooseus .row {
	position: relative;
	z-index: 1;
}

.chooseus__text {
	position: relative;
	z-index: 1;
}

.chooseus__text .section-title {
	margin-bottom: 10px;
	text-align: left;
}

.chooseus__text .section-title h2 {
	margin-top: 0;
}

.chooseus__text ul {
	margin-bottom: 28px;
}

.chooseus__text ul li {
	list-style: none;
	font-size: 15px;
	color: #727171;
	line-height: 36px;
}

.chooseus__text ul li i {
	color: #db2d2e;
}

.chooseus__video {
	height: 530px;
	width: calc(100% - 46%);
	position: absolute;
	right: 0;
	top: 0;
}

.chooseus__video img {
	height: 100%;
	width: 100%;
}

.chooseus__video .play-btn {
	height: 60px;
	width: 60px;
	background: #db2d2e;
	border-radius: 50%;
	font-size: 25px;
	color: #ffffff;
	line-height: 64px;
	text-align: center;
	display: inline-block;
	position: absolute;
	left: 50%;
	top: 50%;
	-webkit-transform: translate(-30px, -30px);
	-ms-transform: translate(-30px, -30px);
	transform: translate(-30px, -30px);
	z-index: 9;
}

.chooseus__video .play-btn i {
	position: relative;
	left: 3px;
	top: -1px;
}

/*---------------------
  Cta
-----------------------*/

.cta {
	padding-bottom: 70px;
}

.cta__item {
	position: relative;
	z-index: 1;
	padding: 50px 34px 65px;
	border-radius: 2px;
	margin-bottom: 30px;
}

.cta__item:before {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background: rgba(0, 0, 0, 0.3);
	content: "";
	z-index: -1;
	border-radius: 2px;
}

.cta__item h4 {
	color: #ffffff;
	font-weight: 700;
	margin-bottom: 6px;
}

.cta__item p {
	color: #ffffff;
	margin-bottom: 0;
}

/*---------------------
  Car
-----------------------*/

.car {
	padding-bottom: 120px;
}

.car .section-title {
	margin-bottom: 20px;
}

.car .section-title h2 {
	margin-bottom: 0;
}

.filter__controls {
	text-align: center;
	margin-bottom: 40px;
}

.filter__controls li {
	list-style: none;
	display: inline-block;
	font-size: 13px;
	text-transform: uppercase;
	color: #8d8d8d;
	font-weight: 700;
	margin-right: 50px;
	position: relative;
	padding: 4px 0;
	cursor: pointer;
}

.filter__controls li.active {
	color: #323232;
}

.filter__controls li.active:after {
	opacity: 1;
}

.filter__controls li:after {
	position: absolute;
	left: 0;
	bottom: 0;
	height: 2px;
	width: 100%;
	background: #db2d2e;
	content: "";
	opacity: 0;
}

.filter__controls li:last-child {
	margin-right: 0;
}

.car__filter__option {
	background: #f7f7f7;
	padding: 12px 30px;
	margin-bottom: 30px;
}

.car__filter__option .car__filter__option__item.car__filter__option__item--right {
	text-align: right;
}

.car__filter__option .car__filter__option__item h6 {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
	display: inline-block;
	margin-right: 15px;
}

.car__filter__option .car__filter__option__item .nice-select {
	width: auto;
	font-size: 15px;
	color: #353535;
	padding-left: 20px;
	padding-right: 50px;
	border: 1px solid #ebebeb;
	height: 36px;
	background: #ffffff;
	line-height: 36px;
	border-radius: 0;
	float: none;
	display: inline-block;
}

.car__filter__option .car__filter__option__item .nice-select .list {
	width: 100%;
	margin-top: 0;
	border-radius: 0;
}

.car__filter__option .car__filter__option__item .nice-select:after {
	border-bottom: 1.5px solid #AAAAB3;
	border-right: 1.5px solid #AAAAB3;
	height: 7px;
	right: 22px;
	width: 7px;
}

.car__item {
	margin-bottom: 30px;
}

.car__item__pic__slider img {
	border-radius: 2px 2px 0 0;
}

.car__item__pic__slider.owl-carousel .owl-dots {
	text-align: center;
	position: absolute;
	width: 100%;
	left: 0;
	bottom: 15px;
}

.car__item__pic__slider.owl-carousel .owl-dots button {
	height: 8px;
	width: 8px;
	background: #8d8d8d;
	border-radius: 50%;
	margin-right: 8px;
	display: inline-block;
}

.car__item__pic__slider.owl-carousel .owl-dots button.active {
	background: #ffffff;
}

.car__item__pic__slider.owl-carousel .owl-dots button:last-child {
	margin-right: 0;
}

.car__item__text {
	border: 1px solid #ebebeb;
}

.car__item__text__inner {
	padding: 20px 0 14px 20px;
}

.car__item__text__inner .label-date {
	display: inline-block;
	font-size: 13px;
	color: #323232;
	font-weight: 700;
	padding: 2px 15px 1px;
	border: 1px solid #ebebeb;
	border-radius: 2px;
}

.car__item__text__inner h5 {
	margin-top: 10px;
	margin-bottom: 14px;
}

.car__item__text__inner h5 a {
	color: #353535;
	font-weight: 700;
}

.car__item__text__inner ul li {
	list-style: none;
	font-size: 15px;
	color: #8d8d8d;
	font-weight: 700;
	position: relative;
	display: inline-block;
	margin-right: 40px;
}

.car__item__text__inner ul li:after {
	position: absolute;
	right: -23px;
	top: 3px;
	height: 15px;
	width: 2px;
	background: #8d8d8d;
	content: "";
}

.car__item__text__inner ul li span {
	color: #323232;
}

.car__item__text__inner ul li:last-child {
	margin-right: 0;
}

.car__item__text__inner ul li:last-child:after {
	display: none;
}

.car__item__price {
	position: relative;
}

.car__item__price .car-option {
	font-size: 15px;
	color: #ffffff;
	font-weight: 700;
	background: #4971FF;
	display: inline-block;
	padding: 12px 22px 10px;
	border-radius: 2px 0 0 2px;
	position: absolute;
	left: 0;
	top: 0;
}

.car__item__price .car-option.sale {
	background: #db2d2e;
}

.car__item__price h6 {
	font-size: 15px;
	color: #db2d2e;
	font-weight: 700;
	border-top: 1px solid #ebebeb;
	padding-left: 120px;
	padding-top: 14px;
	padding-bottom: 11px;
}

.car__item__price h6 span {
	color: #727171;
	font-size: 13px;
	font-weight: 400;
}

.pagination__option {
	padding-top: 20px;
}

.pagination__option a {
	display: inline-block;
	height: 50px;
	width: 50px;
	font-size: 18px;
	color: #727171;
	font-weight: 700;
	border: 1px solid #ebebeb;
	border-radius: 2px;
	line-height: 50px;
	text-align: center;
	margin-right: 6px;
	-webkit-transition: all, 0.3s;
	-o-transition: all, 0.3s;
	transition: all, 0.3s;
}

.pagination__option a:hover,
.pagination__option a.active {
	border-color: #db2d2e;
	color: #353535;
}

.pagination__option a:last-child {
	margin-right: 0;
}

.pagination__option a span {
	font-size: 18px;
	position: relative;
	top: 2px;
	font-weight: 700;
}

.car__sidebar {
	background: #f7f7f7;
	padding: 30px;
}

.car__search {
	margin-bottom: 40px;
}

.car__search h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 15px;
}

.car__search form {
	position: relative;
}

.car__search form input {
	height: 46px;
	width: 100%;
	font-size: 15px;
	color: #727171;
	padding-left: 20px;
	border: 1px solid #ebebeb;
	background: #ffffff;
}

.car__search form input::-webkit-input-placeholder {
	color: #727171;
}

.car__search form input::-moz-placeholder {
	color: #727171;
}

.car__search form input:-ms-input-placeholder {
	color: #727171;
}

.car__search form input::-ms-input-placeholder {
	color: #727171;
}

.car__search form input::placeholder {
	color: #727171;
}

.car__search form button {
	font-size: 14px;
	color: #AAAAB3;
	position: absolute;
	right: 0;
	top: 0;
	background: transparent;
	border: none;
	height: 100%;
	padding: 0 15px;
}

.car__filter h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 15px;
}

.car__filter form .nice-select {
	width: 100%;
	font-size: 15px;
	color: #727171;
	padding-left: 20px;
	border: 1px solid #ebebeb;
	height: 46px;
	background: #ffffff;
	line-height: 46px;
	border-radius: 0;
	margin-bottom: 10px;
	float: none;
}

.car__filter form .nice-select .list {
	width: 100%;
	margin-top: 0;
	border-radius: 0;
}

.car__filter form .nice-select:after {
	border-bottom: 1.5px solid #AAAAB3;
	border-right: 1.5px solid #AAAAB3;
	height: 7px;
	right: 22px;
	width: 7px;
}

.car__filter form .filter-price {
	margin-bottom: 30px;
	padding-top: 8px;
}

.car__filter form .filter-price p {
	color: #323232;
	margin-bottom: 5px;
	margin-bottom: 18px;
}

.car__filter form .filter-price .price-range-wrap {
	position: relative;
}

.car__filter form .filter-price .price-range-wrap .ui-widget-content {
	height: 4px;
	background: rgba(0, 0, 0, 0.1);
	border: none;
	border-radius: 1px;
}

.car__filter form .filter-price .price-range-wrap .ui-slider-horizontal .ui-slider-handle {
	top: -5px;
	margin-left: -4px;
}

.car__filter form .filter-price .price-range-wrap .ui-slider .ui-slider-handle {
	width: 14px;
	height: 14px;
	background: #db2d2e;
	border-radius: 50%;
	cursor: pointer;
}

.car__filter form .filter-price .price-range-wrap .ui-state-default,
.car__filter form .filter-price .price-range-wrap .ui-widget-content .ui-state-default,
.car__filter form .filter-price .price-range-wrap .ui-widget-header .ui-state-default,
.car__filter form .filter-price .price-range-wrap .ui-button,
.car__filter form .filter-price .price-range-wrap html .ui-button.ui-state-disabled:hover,
.car__filter form .filter-price .price-range-wrap html .ui-button.ui-state-disabled:active {
	border: none;
}

.car__filter form .filter-price .price-range-wrap .ui-slider .ui-slider-range {
	background: #db2d2e;
}

.car__filter form .filter-price .price-input {
	position: absolute;
	left: 42px;
	top: -44px;
}

.car__filter form .filter-price .price-input input {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
	border: none;
	width: 200px;
	background: transparent;
}

.car__filter form .car__filter__btn {
	text-align: center;
}

/*---------------------
  Footer
-----------------------*/

.footer {
	padding-top: 60px;
	padding-bottom: 30px;
}

.footer__contact {
	padding-bottom: 60px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.2);
	margin-bottom: 65px;
}

.footer__contact__title h2 {
	font-size: 40px;
	color: #ffffff;
	font-weight: 700;
}

.footer__contact__option {
	text-align: right;
}

.footer__contact__option .option__item {
	font-size: 15px;
	color: #ffffff;
	background: #db2d2e;
	display: inline-block;
	font-weight: 700;
	padding: 14px 18px 14px 24px;
	margin-right: 16px;
	border-radius: 2px;
}

.footer__contact__option .option__item:last-child {
	margin-right: 0;
}

.footer__contact__option .option__item.email {
	color: #323232;
	background: #ffffff;
}

.footer__contact__option .option__item.email i {
	color: #db2d2e;
}

.footer__contact__option .option__item i {
	margin-right: 5px;
}

.footer__about {
	margin-bottom: 30px;
}

.footer__about p {
	color: #8d8d8d;
	margin-bottom: 25px;
}

.footer__logo {
	margin-bottom: 20px;
}

.footer__logo a {
	display: inline-block;
}

.footer__social {
	margin-bottom: 20px;
}

.footer__social a {
	display: inline-block;
	height: 38px;
	width: 38px;
	font-size: 15px;
	color: #ffffff;
	line-height: 38px;
	text-align: center;
	border-radius: 50%;
	margin-right: 6px;
}

.footer__social a:last-child {
	margin-right: 0;
}

.footer__social a.facebook {
	background: #324975;
}

.footer__social a.twitter {
	background: #2C85AE;
}

.footer__social a.google {
	background: #DC4438;
}

.footer__social a.skype {
	background: #3EAFF0;
}

.footer__copyright__text {
	margin-bottom: 0 !important;
}

.footer__copyright__text a {
	font-weight: 700;
	color: #db2d2e;
}

.footer__copyright__text i {
	color: #cc1111;
}

.footer__widget {
	margin-bottom: 30px;
}

.footer__widget h5 {
	color: #ffffff;
	font-weight: 700;
	margin-bottom: 8px;
}

.footer__widget ul li {
	list-style: none;
}

.footer__widget ul li a {
	font-size: 15px;
	color: #8d8d8d;
	line-height: 36px;
}

.footer__widget ul li i {
	margin-right: 2px;
}

.footer__brand {
	overflow: hidden;
	margin-bottom: 30px;
}

.footer__brand h5 {
	color: #ffffff;
	font-weight: 700;
	margin-bottom: 8px;
}

.footer__brand ul {
	width: 50%;
	float: left;
}

.footer__brand ul li {
	list-style: none;
}

.footer__brand ul li a {
	font-size: 15px;
	color: #8d8d8d;
	line-height: 36px;
}

.footer__brand ul li i {
	margin-right: 2px;
}

/*---------------------
  Breadcrumb
-----------------------*/

.breadcrumb-option {
	padding: 70px 0 60px;
}

.breadcrumb-option.contact-breadcrumb {
	padding: 70px 0 260px;
}

.breadcrumb-option.contact-breadcrumb h2 {
	margin-bottom: 0;
}

.breadcrumb__text h2 {
	color: #ffffff;
	font-weight: 700;
	font-size: 46px;
	margin-bottom: 6px;
}

.breadcrumb__links a {
	font-size: 15px;
	color: #ffffff;
	margin-right: 18px;
	display: inline-block;
	position: relative;
}

.breadcrumb__links a:after {
	position: absolute;
	right: -15px;
	top: 0;
	content: "";
	font-family: "FontAwesome";
}

.breadcrumb__links a i {
	color: #db2d2e;
	margin-right: 5px;
}

.breadcrumb__links span {
	font-size: 15px;
	color: #727171;
	display: inline-block;
}

/*---------------------
  About
-----------------------*/

.about {
	padding-bottom: 70px;
}

.section-title.about-title h2 {
	line-height: 55px;
}

.section-title.about-title p {
	font-size: 17px;
	line-height: 30px;
	color: #6A6B7C;
}

.about__feature {
	padding-bottom: 60px;
}

.about__feature__item {
	text-align: center;
	margin-bottom: 30px;
	padding: 0 20px;
}

.about__feature__item h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-top: 30px;
	margin-bottom: 15px;
}

.about__feature__item p {
	margin-bottom: 0;
}

.about__pic {
	margin-bottom: 50px;
}

.about__pic img {
	min-width: 100%;
	border-radius: 10px;
}

.about__item {
	margin-bottom: 30px;
}

.about__item h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 15px;
}

.about__item p {
	margin-bottom: 0;
}

/*---------------------
  Call
-----------------------*/

.call__text .section-title {
	text-align: left;
	margin-bottom: 35px;
}

.call__text .section-title h2 {
	color: #ffffff;
	margin-bottom: 12px;
}

.call__text .section-title p {
	color: #ffffff;
}

.call__text a {
	font-size: 15px;
	color: #ffffff;
	font-weight: 700;
	text-transform: uppercase;
	padding: 6px 0;
	position: relative;
}

.call__text a:after {
	position: absolute;
	left: 0;
	bottom: 0;
	height: 2px;
	width: 100%;
	background: #db2d2e;
	content: "";
}

.call__form input {
	width: 100%;
	height: 48px;
	color: #727171;
	font-size: 15px;
	padding-left: 20px;
	border: none;
	border-radius: 2px;
	margin-bottom: 25px;
}

.call__form .nice-select {
	float: none;
	width: 100%;
	height: 48px;
	border-radius: 2px;
	line-height: 48px;
	margin-bottom: 25px;
}

.call__form .nice-select span {
	font-size: 15px;
	color: #353535;
}

.call__form .nice-select .list {
	width: 100%;
	margin-top: 0;
	border-radius: 0;
}

.call__form .nice-select:after {
	height: 10px;
	width: 10px;
	border-bottom: 2px solid #323232;
	border-right: 2px solid #323232;
	margin-top: 0;
	right: 22px;
	top: 35%;
}

.call__form button {
	letter-spacing: 1px;
}

/*---------------------
  Team
-----------------------*/

.team {
	padding-bottom: 70px;
}

.section-title.team-title {
	margin-bottom: 50px;
}

.team__item {
	margin-bottom: 30px;
}

.team__item__pic img {
	min-width: 100%;
	border-radius: 2px;
}

.team__item__text {
	padding-top: 20px;
	text-align: center;
}

.team__item__text h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 5px;
}

.team__item__text span {
	font-size: 15px;
	color: #727171;
}

/*---------------------
  Testimonial
-----------------------*/

.testimonial {
	padding-top: 0;
}

.section-title.testimonial-title {
	margin-bottom: 40px;
	padding-top: 90px;
	border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.testimonial__slider.owl-carousel .col-lg-6 {
	max-width: 100%;
}

.testimonial__slider.owl-carousel .owl-stage-outer {
	padding-top: 30px;
	padding-bottom: 40px;
}

.testimonial__slider.owl-carousel .owl-nav button {
	height: 50px;
	width: 50px;
	border-radius: 50%;
	background: #f4f6f8;
	line-height: 50px;
	font-size: 30px;
	color: #353535;
	text-align: center;
	position: absolute;
	left: -85px;
	top: 50%;
	-webkit-transform: translateY(-40px);
	-ms-transform: translateY(-40px);
	transform: translateY(-40px);
}

.testimonial__slider.owl-carousel .owl-nav button.owl-next {
	left: auto;
	right: -85px;
}

.testimonial__slider.owl-carousel .owl-dots {
	text-align: center;
}

.testimonial__slider.owl-carousel .owl-dots button {
	height: 10px;
	width: 10px;
	background: #ebebeb;
	border-radius: 50%;
	display: inline-block;
	margin-right: 10px;
}

.testimonial__slider.owl-carousel .owl-dots button.active {
	background: #db2d2e;
}

.testimonial__slider.owl-carousel .owl-dots button:last-child {
	margin-right: 0;
}

.testimonial__item {
	-webkit-box-shadow: 0px 6px 15px rgba(50, 15, 15, 0.05);
	box-shadow: 0px 6px 15px rgba(50, 15, 15, 0.05);
	padding: 40px 30px 65px 40px;
	border-radius: 5px;
}

.testimonial__item p {
	font-size: 20px;
	color: #353535;
	line-height: 32px;
	margin-bottom: 0;
}

.testimonial__item__author {
	overflow: hidden;
	margin-bottom: 22px;
}

.testimonial__item__author__pic {
	float: left;
	margin-right: 30px;
}

.testimonial__item__author__text {
	overflow: hidden;
	padding-top: 10px;
}

.testimonial__item__author__text .rating {
	margin-bottom: 5px;
}

.testimonial__item__author__text .rating i {
	font-size: 20px;
	color: #F9B944;
}

.testimonial__item__author__text h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
}

.testimonial__item__author__text h5 span {
	font-size: 15px;
	color: #727171;
	font-weight: 400;
}

/*---------------------
  Counter
-----------------------*/

.counter {
	position: relative;
	z-index: 1;
	padding-bottom: 60px;
	padding-top: 80px;
}

.counter:after {
	position: absolute;
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	background: rgba(0, 0, 0, 0.3);
	content: "";
	z-index: -1;
}

.counter__item {
	text-align: center;
	margin-bottom: 30px;
}

.counter__item h2 {
	font-size: 60px;
	font-weight: 700;
	color: #ffffff;
	display: inline-block;
}

.counter__item strong {
	font-size: 60px;
	font-weight: 700;
	color: #ffffff;
	display: inline-block;
	line-height: 72px;
}

.counter__item p {
	font-size: 20px;
	margin-bottom: 0;
	text-transform: uppercase;
	margin-top: 5px;
	font-weight: 700;
	color: #ffffff;
}

/*---------------------
  Clients
-----------------------*/

.clients {
	padding-bottom: 70px;
}

.section-title.client-title {
	margin-bottom: 45px;
}

.client__item {
	border: 1px solid #ebebeb;
	margin-bottom: 30px;
	height: 110px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
}

/*---------------------
  About
-----------------------*/

/*---------------------
  Services
-----------------------*/

.services__item {
	padding-bottom: 60px;
}

.services__item {
	text-align: center;
	padding: 36px 35px 40px;
	border: 1px solid #ebebeb;
	border-radius: 2px;
	-webkit-transition: all, 0.4s;
	-o-transition: all, 0.4s;
	transition: all, 0.4s;
	margin-bottom: 40px;
}

.services__item:hover {
	-webkit-box-shadow: 0px 6px 50px rgba(50, 15, 15, 0.05);
	box-shadow: 0px 6px 50px rgba(50, 15, 15, 0.05);
	border: 1px solid transparent;
}

.services__item:hover a {
	background: #db2d2e;
	border-color: #db2d2e;
	color: #ffffff;
}

.services__item h5 {
	font-size: 20px;
	color: #323232;
	font-weight: 700;
	margin-bottom: 12px;
	margin-top: 20px;
}

.services__item p {
	margin-bottom: 20px;
	color: #8d8d8d;
}

.services__item a {
	font-size: 15px;
	color: #db2d2e;
	height: 40px;
	width: 40px;
	line-height: 40px;
	text-align: center;
	border-radius: 50%;
	border: 1px solid #ebebeb;
	display: inline-block;
	-webkit-transition: all, 0.3s;
	-o-transition: all, 0.3s;
	transition: all, 0.3s;
}

/*---------------------
  Car Details
-----------------------*/

.car-details {
	padding-bottom: 70px;
}

.car__details__pic {
	margin-bottom: 50px;
}

.car__details__pic__large {
	margin-bottom: 20px;
}

.car__details__pic__large img {
	min-width: 100%;
}

.car-thumbs .ct img {
	cursor: pointer;
}

.car__details__tab .nav-tabs {
	border-bottom: none;
	background: #f7f7f7;
	padding: 0 30px;
}

.car__details__tab .nav-tabs .nav-item {
	display: inline-block;
	margin-right: 62px;
}

.car__details__tab .nav-tabs .nav-item:last-child {
	margin-right: 0;
}

.car__details__tab .nav-tabs .nav-item .nav-link {
	font-size: 20px;
	color: #707070;
	font-weight: 700;
	border: none;
	border-top-left-radius: 0;
	border-top-right-radius: 0;
	padding: 22px 0 16px;
	position: relative;
}

.car__details__tab .nav-tabs .nav-item .nav-link:after {
	position: absolute;
	left: 0;
	bottom: 0;
	height: 4px;
	width: 100%;
	background: #db2d2e;
	content: "";
	opacity: 0;
}

.car__details__tab .nav-tabs .nav-item .nav-link.active {
	background-color: transparent;
}

.car__details__tab .nav-tabs .nav-item .nav-link.active:after {
	opacity: 1;
}

.car__details__tab .tab-content {
	padding-top: 45px;
}

.car__details__tab__info {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: 40px;
}

.car__details__tab__info__item {
	margin-bottom: 30px;
}

.car__details__tab__info__item h5 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 6px;
}

.car__details__tab__info__item ul li {
	list-style: none;
	font-size: 15px;
	color: #707070;
	line-height: 36px;
}

.car__details__tab__info__item ul li i {
	color: #db2d2e;
	margin-right: 8px;
	font-size: 10px;
}

.car__details__tab__feature__item {
	margin-bottom: 30px;
}

.car__details__tab__feature__item h5 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 6px;
}

.car__details__tab__feature__item ul li {
	list-style: none;
	font-size: 15px;
	color: #707070;
	line-height: 36px;
}

.car__details__tab__feature__item ul li i {
	color: #db2d2e;
	margin-right: 8px;
	font-size: 10px;
}

.car__details__sidebar {
	padding: 25px 30px 20px;
	background: #f7f7f7;
}

.car__details__sidebar__model {
	margin-bottom: 5px;
}

.car__details__sidebar__model ul {
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	padding-bottom: 6px;
	margin-bottom: 20px;
}

.car__details__sidebar__model ul li {
	list-style: none;
	font-size: 15px;
	color: #727171;
	line-height: 36px;
	overflow: hidden;
}

.car__details__sidebar__model ul li span {
	color: #353535;
	font-weight: 700;
	float: right;
}

.car__details__sidebar__model .primary-btn {
	display: block;
	text-align: center;
	border-radius: 0;
	padding: 12px 0 10px;
}

.car__details__sidebar__model .primary-btn i {
	margin-right: 5px;
}

.car__details__sidebar__model p {
	color: #353535;
	padding: 10px 0;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	text-align: center;
	margin-bottom: 0;
}

.car__details__sidebar__payment ul {
	margin-bottom: 30px;
}

.car__details__sidebar__payment ul li {
	list-style: none;
	font-size: 15px;
	color: #727171;
	line-height: 36px;
	overflow: hidden;
}

.car__details__sidebar__payment ul li:last-child span {
	font-size: 20px;
}

.car__details__sidebar__payment ul li span {
	color: #353535;
	font-weight: 700;
	float: right;
}

.car__details__sidebar__payment .primary-btn {
	display: block;
	border-radius: 0;
	text-align: center;
	margin-bottom: 10px;
	padding: 12px 0 10px;
}

.car__details__sidebar__payment .primary-btn i {
	margin-right: 5px;
}

.car__details__sidebar__payment .primary-btn.sidebar-btn {
	background: #ffffff;
	color: #727171;
	border: 1px solid #ebebeb;
}

/*---------------------
  Latest Blog
-----------------------*/

.latest {
	padding-bottom: 70px;
	padding-top: 160px;
}

.latest .section-title {
	margin-bottom: 45px;
}

.latest__blog__item {
	margin-bottom: 30px;
}

.latest__blog__item__pic {
	height: 220px;
	position: relative;
	border-radius: 2px;
}

.latest__blog__item__pic ul {
	background: rgba(0, 0, 0, 0.5);
	padding: 8px 20px 10px;
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	border-radius: 0 0 2px 2px;
}

.latest__blog__item__pic ul li {
	list-style: none;
	display: inline-block;
	font-size: 13px;
	color: #ffffff;
	margin-right: 22px;
	position: relative;
}

.latest__blog__item__pic ul li:after {
	position: absolute;
	right: -14px;
	top: 1px;
	height: 17px;
	width: 1px;
	background: #ffffff;
	content: "";
}

.latest__blog__item__pic ul li:last-child {
	margin-right: 0;
}

.latest__blog__item__pic ul li:last-child:after {
	display: none;
}

.latest__blog__item__text {
	padding-top: 25px;
}

.latest__blog__item__text h5 {
	font-size: 20px;
	color: #323232;
	font-weight: 700;
	line-height: 28px;
	margin-bottom: 14px;
}

.latest__blog__item__text p {
	color: #8d8d8d;
	margin-bottom: 20px;
}

.latest__blog__item__text a {
	font-size: 15px;
	color: #8d8d8d;
	font-weight: 700;
	display: inline-block;
}

.latest__blog__item__text a i {
	color: #db2d2e;
	margin-left: 6px;
}

/*---------------------
  Blog
-----------------------*/

.blog .pagination__option {
	padding-top: 10px;
}

.blog__item {
	margin-bottom: 35px;
}

.blog__item__pic {
	height: 220px;
	position: relative;
	border-radius: 2px;
}

.blog__item__pic ul {
	background: rgba(0, 0, 0, 0.5);
	padding: 8px 22px 10px;
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	border-radius: 0 0 2px 2px;
}

.blog__item__pic ul li {
	list-style: none;
	display: inline-block;
	font-size: 13px;
	color: #ffffff;
	margin-right: 28px;
	position: relative;
}

.blog__item__pic ul li:after {
	position: absolute;
	right: -19px;
	top: 1px;
	height: 17px;
	width: 1px;
	background: #ffffff;
	content: "";
}

.blog__item__pic ul li:last-child {
	margin-right: 0;
}

.blog__item__pic ul li:last-child:after {
	display: none;
}

.blog__item__text {
	padding-top: 28px;
}

.blog__item__text h5 {
	margin-bottom: 40px;
}

.blog__item__text h5 a {
	font-size: 20px;
	color: #323232;
	font-weight: 700;
}

.blog__item__text p {
	color: #8d8d8d;
	margin-bottom: 0;
}

/*---------------------
  Blog Sidebar
-----------------------*/

.blog__sidebar__search {
	position: relative;
	margin-bottom: 50px;
}

.blog__sidebar__search input {
	height: 50px;
	width: 100%;
	font-size: 15px;
	color: #8d8d8d;
	padding-left: 20px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	background: #ffffff;
	border-radius: 0.5px;
}

.blog__sidebar__search input::-webkit-input-placeholder {
	color: #8d8d8d;
}

.blog__sidebar__search input::-moz-placeholder {
	color: #8d8d8d;
}

.blog__sidebar__search input:-ms-input-placeholder {
	color: #8d8d8d;
}

.blog__sidebar__search input::-ms-input-placeholder {
	color: #8d8d8d;
}

.blog__sidebar__search input::placeholder {
	color: #8d8d8d;
}

.blog__sidebar__search button {
	font-size: 16px;
	color: #353535;
	position: absolute;
	right: 0;
	top: 0;
	background: transparent;
	border: none;
	height: 100%;
	padding: 0 15px;
}

.blog__sidebar__feature {
	margin-bottom: 45px;
}

.blog__sidebar__feature h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 36px;
}

.blog__sidebar__feature__item {
	padding-bottom: 20px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: 25px;
}

.blog__sidebar__feature__item:last-child {
	padding-bottom: 0;
	border-bottom: none;
	margin-bottom: 0;
}

.blog__sidebar__feature__item h6 {
	margin-bottom: 28px;
}

.blog__sidebar__feature__item h6 a {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
}

.blog__sidebar__feature__item ul li {
	font-size: 13px;
	color: #8d8d8d;
	display: inline-block;
	list-style: none;
	margin-right: 22px;
	position: relative;
}

.blog__sidebar__feature__item ul li:after {
	position: absolute;
	right: -14px;
	top: 4px;
	height: 13px;
	width: 1px;
	background: #8d8d8d;
	content: "";
}

.blog__sidebar__feature__item ul li:last-child {
	margin-right: 0;
}

.blog__sidebar__feature__item ul li:last-child:after {
	display: none;
}

.blog__sidebar__categories {
	margin-bottom: 30px;
}

.blog__sidebar__categories h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 10px;
}

.blog__sidebar__categories ul li {
	list-style: none;
	position: relative;
	padding-left: 10px;
}

.blog__sidebar__categories ul li:before {
	position: absolute;
	left: 0;
	top: 16px;
	height: 4px;
	width: 4px;
	background: #db2d2e;
	content: "";
	border-radius: 50%;
}

.blog__sidebar__categories ul li a {
	font-size: 15px;
	color: #727171;
	line-height: 36px;
}

.blog__sidebar__tag {
	margin-bottom: 70px;
}

.blog__sidebar__tag h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 20px;
}

.blog__sidebar__tag a {
	display: inline-block;
	font-size: 13px;
	color: #8d8d8d;
	font-weight: 700;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-right: 12px;
	position: relative;
	-webkit-transition: all, 0.3s;
	-o-transition: all, 0.3s;
	transition: all, 0.3s;
}

.blog__sidebar__tag a:hover {
	color: #db2d2e;
}

.blog__sidebar__tag a:after {
	position: absolute;
	right: -10px;
	top: 9px;
	height: 2px;
	width: 4px;
	background: #707070;
	content: "";
}

.blog__sidebar__newslatter h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 20px;
}

.blog__sidebar__newslatter p {
	color: #8d8d8d;
	margin-bottom: 35px;
}

.blog__sidebar__newslatter form {
	position: relative;
}

.blog__sidebar__newslatter form input {
	width: 100%;
	height: 46px;
	font-size: 15px;
	color: #727171;
	padding-left: 20px;
	border-radius: 2px;
	padding-right: 10px;
	border: 1px solid #ebebeb;
}

.blog__sidebar__newslatter form button {
	font-size: 13px;
	color: #ffffff;
	background: #db2d2e;
	font-weight: 700;
	display: inline-block;
	padding: 8px 20px 11px;
	position: absolute;
	right: 0;
	top: 4px;
	border: none;
	border-radius: 2px;
	margin-right: 4px;
}

/*---------------------
  Blog Hero
-----------------------*/

.blog-details-hero {
	padding-top: 80px;
	padding-bottom: 400px;
}

.blog__details__hero__text {
	text-align: center;
}

.blog__details__hero__text .label {
	font-size: 15px;
	color: #db2d2e;
	font-weight: 700;
	text-transform: uppercase;
}

.blog__details__hero__text h2 {
	font-size: 40px;
	color: #ffffff;
	font-weight: 700;
	line-height: 55px;
	margin-top: 12px;
	margin-bottom: 20px;
}

.blog__details__hero__text ul li {
	list-style: none;
	font-size: 15px;
	color: #ffffff;
	display: inline-block;
	margin-right: 90px;
}

.blog__details__hero__text ul li span {
	display: inline-block;
	position: relative;
}

.blog__details__hero__text ul li span::after {
	position: absolute;
	right: -48px;
	top: 3px;
	height: 15px;
	width: 1px;
	background: #ffffff;
	content: "";
}

.blog__details__hero__text ul li:last-child {
	margin-right: 0;
}

.blog__details__hero__text ul li:last-child span::after {
	display: none;
}

.blog__details__hero__text ul li img {
	height: 34px;
	width: 34px;
	border-radius: 50%;
	margin-right: 6px;
}

.blog__details__hero__text ul li i {
	color: #db2d2e;
	margin-right: 2px;
}

/*---------------------
  Blog Details
-----------------------*/

.blog-details {
	margin-top: -360px;
	padding-top: 0;
	overflow: hidden;
}

.blog__details__pic {
	margin-bottom: 95px;
}

.blog__details__pic img {
	min-width: 100%;
}

.blog__details__text {
	margin-bottom: 30px;
}

.blog__details__text p {
	font-size: 17px;
	line-height: 30px;
}

.blog__details__text p:first-child {
	margin-bottom: 25px;
}

.blog__details__text p:last-child {
	margin-bottom: 0;
}

.blog__details__quote {
	padding: 0 60px;
	margin-bottom: 60px;
}

.blog__details__quote p {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	line-height: 35px;
	position: relative;
	padding-left: 34px;
	margin-bottom: 0;
}

.blog__details__quote p:before {
	position: absolute;
	left: 0;
	top: 0;
	height: 94px;
	width: 3px;
	background: #db2d2e;
	content: "";
}

.blog__details__desc {
	margin-bottom: 42px;
}

.blog__details__desc p {
	font-size: 17px;
	line-height: 30px;
	margin-bottom: 0;
}

.blog__details__share {
	margin-right: -34px;
	overflow: hidden;
	margin-bottom: 40px;
}

.blog__details__share__item {
	display: inline-block;
	background: #37589C;
	border-radius: 2px;
	padding: 10px 10px 8px 16px;
	width: calc(25% - 34px);
	float: left;
	margin-right: 34px;
	margin-bottom: 20px;
}

.blog__details__share__item.twitter {
	background: #54ADF0;
}

.blog__details__share__item.google {
	background: #DC4C39;
}

.blog__details__share__item.linkedin {
	background: #0179B6;
}

.blog__details__share__item i {
	font-size: 18px;
	color: #ffffff;
	border-right: 1px solid rgba(255, 255, 255, 0.1);
	padding-right: 15px;
	display: inline-block;
}

.blog__details__share__item span {
	font-size: 15px;
	color: #ffffff;
	font-weight: 600;
	display: inline-block;
	margin-left: 10px;
}

.blog__details__author {
	overflow: hidden;
	margin-bottom: 60px;
}

.blog__details__author__pic {
	float: left;
	margin-right: 40px;
}

.blog__details__author__pic img {
	height: 100px;
	width: 100px;
	border-radius: 50%;
}

.blog__details__author__text {
	overflow: hidden;
}

.blog__details__author__text h5 {
	font-size: 20px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 12px;
}

.blog__details__author__text p {
	font-size: 17px;
	line-height: 30px;
	margin-bottom: 0;
}

.blog__details__btns {
	margin-bottom: 35px;
}

.blog__details__btns__item {
	display: block;
	border-radius: 2px;
	padding: 25px 20px 20px;
	margin-bottom: 20px;
}

.blog__details__btns__item h6 {
	font-size: 15px;
	color: #ffffff;
	font-weight: 700;
	margin-bottom: 26px;
}

.blog__details__btns__item ul li {
	font-size: 13px;
	color: #ffffff;
	display: inline-block;
	list-style: none;
	margin-right: 28px;
	position: relative;
}

.blog__details__btns__item ul li:after {
	position: absolute;
	right: -19px;
	top: 4px;
	height: 13px;
	width: 1px;
	background: #ffffff;
	content: "";
}

.blog__details__btns__item ul li:last-child {
	margin-right: 0;
}

.blog__details__btns__item ul li:last-child:after {
	display: none;
}

.blog__details__comment {
	margin-bottom: 60px;
}

.blog__details__comment h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 35px;
}

.blog__details__comment__item {
	overflow: hidden;
	margin-bottom: 30px;
}

.blog__details__comment__item.reply__comment {
	padding-top: 30px;
	border-top: 1px solid rgba(0, 0, 0, 0.1);
	margin-left: 100px;
}

.blog__details__comment__item:last-child {
	margin-bottom: 0;
}

.blog__details__comment__item__pic {
	float: left;
	margin-right: 30px;
}

.blog__details__comment__item__pic img {
	height: 70px;
	width: 70px;
	border-radius: 50%;
}

.blog__details__comment__item__text {
	overflow: hidden;
}

.blog__details__comment__item__text h6 {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
	margin-bottom: 10px;
}

.blog__details__comment__item__text p {
	margin-bottom: 35px;
}

.blog__details__comment__item__text a {
	font-size: 15px;
	color: #353535;
	font-weight: 700;
	display: inline-block;
	border: 1px solid rgba(0, 0, 0, 0.1);
	border-radius: 2px;
	padding: 6px 20px 4px;
	margin-right: 6px;
	-webkit-transition: all, 0.3s;
	-o-transition: all, 0.3s;
	transition: all, 0.3s;
}

.blog__details__comment__item__text a:hover {
	background: #db2d2e;
	color: #ffffff;
}

.blog__details__comment__form h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 25px;
}

.blog__details__comment__form form .input-list {
	margin-right: -20px;
	overflow: hidden;
}

.blog__details__comment__form form .input-list-item {
	width: calc(33.33% - 20px);
	float: left;
	margin-right: 20px;
	margin-bottom: 20px;
}

.blog__details__comment__form form .input-list-item p {
	color: #353535;
	margin-bottom: 5px;
}

.blog__details__comment__form form .input-list-item input {
	width: 100%;
	height: 46px;
	font-size: 15px;
	color: #353535;
	border-radius: 2px;
	padding-left: 20px;
	border: 1px solid rgba(0, 0, 0, 0.1);
}

.blog__details__comment__form form .input-desc {
	width: 100%;
	margin-bottom: 24px;
}

.blog__details__comment__form form .input-desc p {
	color: #353535;
	margin-bottom: 5px;
}

.blog__details__comment__form form .input-desc textarea {
	width: 100%;
	height: 140px;
	font-size: 15px;
	color: #353535;
	border-radius: 2px;
	padding-left: 20px;
	padding-top: 12px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	resize: none;
}

/*---------------------
  Contact
-----------------------*/

.contact__text {
	margin-bottom: 30px;
}

.contact__text .section-title {
	text-align: left;
	margin-bottom: 36px;
}

.contact__text ul li {
	font-size: 15px;
	color: #353535;
	line-height: 36px;
	list-style: none;
}

.contact__text ul li span {
	color: #727171;
	width: 85px;
	display: inline-block;
}

.contact__form form input {
	width: 100%;
	height: 46px;
	font-size: 15px;
	color: #727171;
	border-radius: 2px;
	padding-left: 20px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: 30px;
}

.contact__form form input::-webkit-input-placeholder {
	color: #727171;
}

.contact__form form input::-moz-placeholder {
	color: #727171;
}

.contact__form form input:-ms-input-placeholder {
	color: #727171;
}

.contact__form form input::-ms-input-placeholder {
	color: #727171;
}

.contact__form form input::placeholder {
	color: #727171;
}

.contact__form form textarea {
	width: 100%;
	height: 100px;
	font-size: 15px;
	color: #727171;
	border-radius: 2px;
	padding-left: 20px;
	padding-top: 12px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	margin-bottom: 34px;
	resize: none;
}

.contact__form form textarea::-webkit-input-placeholder {
	color: #727171;
}

.contact__form form textarea::-moz-placeholder {
	color: #727171;
}

.contact__form form textarea:-ms-input-placeholder {
	color: #727171;
}

.contact__form form textarea::-ms-input-placeholder {
	color: #727171;
}

.contact__form form textarea::placeholder {
	color: #727171;
}

/*---------------------
  Contact Address
-----------------------*/

.contact-address {
	padding-bottom: 70px;
}

.contact__address__text {
	border-top: 1px solid rgba(0, 0, 0, 0.1);
	padding-top: 100px;
}

.contact__address__item {
	margin-bottom: 30px;
}

.contact__address__item h4 {
	color: #353535;
	font-weight: 700;
	margin-bottom: 18px;
}

.contact__address__item p {
	line-height: 32px;
	margin-bottom: 5px;
}

.contact__address__item span {
	display: block;
	font-size: 18px;
	color: #353535;
	font-weight: 700;
}

/*--------------------------------- Responsive Media Quaries -----------------------------*/

@media only screen and (min-width: 1200px) {
	.container {
		max-width: 1170px;
	}
}

/* Medium Device = 1200px */

@media only screen and (min-width: 992px) and (max-width: 1199px) {
	.header__menu ul li {
		margin-right: 32px;
	}
	.chooseus {
		height: auto;
	}
	.chooseus__video {
		height: 100%;
	}
	.car__filter form .filter-price .price-input {
		position: relative;
		left: 0;
		top: 0;
		margin-top: 20px;
	}
	.car__details__tab .nav-tabs .nav-item {
		margin-right: 8px;
	}
	.testimonial__slider.owl-carousel .owl-nav button {
		left: -10px;
	}
	.testimonial__slider.owl-carousel .owl-nav button.owl-next {
		right: -10px;
	}
}

/* Tablet Device = 768px */

@media only screen and (min-width: 768px) and (max-width: 991px) {
	.hero__text {
		padding-top: 0;
		margin-bottom: 40px;
	}
	.feature__text {
		margin-bottom: 500px;
	}
	.feature__item {
		float: none;
	}
	.chooseus {
		height: auto;
		padding-bottom: 0;
	}
	.chooseus__video {
		height: 100%;
		width: 100%;
		position: relative;
	}
	.chooseus__text {
		margin-bottom: 40px;
	}
	.car__sidebar {
		margin-bottom: 40px;
	}
	.car__details__tab .nav-tabs .nav-item {
		margin-right: 8px;
	}
	.blog__sidebar {
		padding-top: 45px;
	}
	.testimonial__slider.owl-carousel .owl-nav button {
		left: -10px;
	}
	.testimonial__slider.owl-carousel .owl-nav button.owl-next {
		right: -10px;
	}
	.header__top {
		display: none;
	}
	.header__nav {
		display: none;
	}
	.header .container {
		position: relative;
	}
	.canvas__open {
		display: block;
		font-size: 22px;
		color: #323232;
		height: 35px;
		width: 35px;
		line-height: 35px;
		text-align: center;
		border: 1px solid #323232;
		border-radius: 2px;
		cursor: pointer;
		position: absolute;
		right: 15px;
		top: 35px;
	}
	.offcanvas-menu-overlay {
		position: fixed;
		left: 0;
		top: 0;
		height: 100%;
		width: 100%;
		background: rgba(0, 0, 0, 0.7);
		content: "";
		z-index: 98;
		-webkit-transition: all, 0.5s;
		-o-transition: all, 0.5s;
		transition: all, 0.5s;
		visibility: hidden;
	}
	.offcanvas-menu-overlay.active {
		visibility: visible;
	}
	.offcanvas-menu-wrapper {
		position: fixed;
		left: -300px;
		width: 300px;
		height: 100%;
		background: #ffffff;
		padding: 50px 30px 30px;
		display: block;
		z-index: 99;
		overflow-y: auto;
		-webkit-transition: all, 0.5s;
		-o-transition: all, 0.5s;
		transition: all, 0.5s;
		opacity: 0;
	}
	.offcanvas-menu-wrapper.active {
		opacity: 1;
		left: 0;
	}
	.offcanvas__menu {
		display: none;
	}
	.slicknav_btn {
		display: none;
	}
	.slicknav_menu {
		background: transparent;
		padding: 0;
		margin-bottom: 30px;
	}
	.slicknav_nav ul {
		margin: 0;
	}
	.slicknav_nav .slicknav_row,
	.slicknav_nav a {
		padding: 7px 0;
		margin: 0;
		color: #353535;
		font-weight: 600;
	}
	.slicknav_nav .slicknav_row:hover {
		border-radius: 0;
		background: transparent;
		color: #353535;
	}
	.slicknav_nav a:hover {
		border-radius: 0;
		background: transparent;
		color: #353535;
	}
	.slicknav_nav {
		display: block !important;
	}
	.offcanvas__logo {
		margin-bottom: 30px;
	}
	.offcanvas__widget {
		margin-bottom: 30px;
	}
	.offcanvas__widget a {
		font-size: 15px;
		color: #353535;
		margin-right: 20px;
		display: inline-block;
	}
	.offcanvas__widget .primary-btn {
		color: #ffffff;
	}
	.offcanvas__widget__add {
		margin-bottom: 20px;
	}
	.offcanvas__widget__add li {
		font-size: 15px;
		color: #353535;
		margin-right: 35px;
		list-style: none;
		line-height: 30px;
	}
	.offcanvas__widget__add li:last-child {
		margin-right: 0;
	}
	.offcanvas__widget__add li i {
		font-size: 18px;
		color: #353535;
		margin-right: 6px;
	}
	.offcanvas__social a {
		display: inline-block;
		font-size: 15px;
		color: #353535;
		margin-right: 16px;
	}
	.offcanvas__social a:last-child {
		margin-right: 0;
	}
	.offcanvas__phone__num {
		margin-bottom: 20px;
	}
	.offcanvas__phone__num i {
		color: #353535;
		margin-right: 6px;
		font-size: 15px;
	}
	.offcanvas__phone__num span {
		font-size: 15px;
		color: #353535;
	}
	.blog-details-hero {
		padding-bottom: 150px;
	}
	.blog-details {
		margin-top: -100px;
	}
}

/* Wide Mobile = 480px */

@media only screen and (max-width: 767px) {
	.hero__text {
		padding-top: 0;
		margin-bottom: 40px;
	}
	.chooseus {
		height: auto;
		padding-bottom: 0;
	}
	.chooseus__video {
		height: 100%;
		width: 100%;
		position: relative;
	}
	.chooseus__text {
		margin-bottom: 40px;
	}
	.footer__contact__option .option__item {
		margin-bottom: 12px;
	}
	.car__sidebar {
		margin-bottom: 40px;
	}
	.blog__sidebar {
		padding-top: 45px;
	}
	.feature:after {
		left: 0%;
		top: 27%;
		width: 100%;
		-webkit-transform: translate(0px, 0px);
		-ms-transform: translate(0px, 0px);
		transform: translate(0px, 0px);
	}
	.feature__text {
		margin-bottom: 500px;
	}
	.feature__item {
		float: none;
	}
	.footer__contact__option {
		text-align: left;
	}
	.footer__contact__title {
		margin-bottom: 20px;
	}
	.car__filter__option .car__filter__option__item.car__filter__option__item--right {
		text-align: left;
		padding-top: 20px;
	}
	.blog__details__share__item {
		width: calc(50% - 34px);
	}
	.blog__details__comment__form form .input-list {
		margin-right: 0;
		overflow: hidden;
	}
	.blog__details__comment__form form .input-list-item {
		width: 100%;
		float: none;
		margin-right: 0;
	}
	.call__text {
		margin-bottom: 40px;
	}
	.testimonial__slider.owl-carousel .owl-nav {
		display: none;
	}
	.blog-details-hero {
		padding-bottom: 150px;
	}
	.blog-details {
		margin-top: -100px;
	}
	.header__top {
		display: none;
	}
	.header__nav {
		display: none;
	}
	.offcanvas-menu-wrapper {
		display: block;
	}
	.header .container {
		position: relative;
	}
	.canvas__open {
		display: block;
		font-size: 22px;
		color: #323232;
		height: 35px;
		width: 35px;
		line-height: 35px;
		text-align: center;
		border: 1px solid #323232;
		border-radius: 2px;
		cursor: pointer;
		position: absolute;
		right: 15px;
		top: 35px;
	}
	.offcanvas-menu-overlay {
		position: fixed;
		left: 0;
		top: 0;
		height: 100%;
		width: 100%;
		background: rgba(0, 0, 0, 0.7);
		content: "";
		z-index: 98;
		-webkit-transition: all, 0.5s;
		-o-transition: all, 0.5s;
		transition: all, 0.5s;
		visibility: hidden;
	}
	.offcanvas-menu-overlay.active {
		visibility: visible;
	}
	.offcanvas-menu-wrapper {
		position: fixed;
		left: -300px;
		width: 300px;
		height: 100%;
		background: #ffffff;
		padding: 50px 30px 30px;
		display: block;
		z-index: 99;
		overflow-y: auto;
		-webkit-transition: all, 0.5s;
		-o-transition: all, 0.5s;
		transition: all, 0.5s;
		opacity: 0;
	}
	.offcanvas-menu-wrapper.active {
		opacity: 1;
		left: 0;
	}
	.offcanvas__menu {
		display: none;
	}
	.slicknav_btn {
		display: none;
	}
	.slicknav_menu {
		background: transparent;
		padding: 0;
		margin-bottom: 30px;
	}
	.slicknav_nav ul {
		margin: 0;
	}
	.slicknav_nav .slicknav_row,
	.slicknav_nav a {
		padding: 7px 0;
		margin: 0;
		color: #353535;
		font-weight: 600;
	}
	.slicknav_nav .slicknav_row:hover {
		border-radius: 0;
		background: transparent;
		color: #353535;
	}
	.slicknav_nav a:hover {
		border-radius: 0;
		background: transparent;
		color: #353535;
	}
	.slicknav_nav {
		display: block !important;
	}
	.offcanvas__logo {
		margin-bottom: 30px;
	}
	.offcanvas__widget {
		margin-bottom: 30px;
	}
	.offcanvas__widget a {
		font-size: 15px;
		color: #353535;
		margin-right: 20px;
		display: inline-block;
	}
	.offcanvas__widget .primary-btn {
		color: #ffffff;
	}
	.offcanvas__widget__add {
		margin-bottom: 20px;
	}
	.offcanvas__widget__add li {
		font-size: 15px;
		color: #353535;
		margin-right: 35px;
		list-style: none;
		line-height: 30px;
	}
	.offcanvas__widget__add li:last-child {
		margin-right: 0;
	}
	.offcanvas__widget__add li i {
		font-size: 18px;
		color: #353535;
		margin-right: 6px;
	}
	.offcanvas__social a {
		display: inline-block;
		font-size: 15px;
		color: #353535;
		margin-right: 16px;
	}
	.offcanvas__social a:last-child {
		margin-right: 0;
	}
	.offcanvas__phone__num {
		margin-bottom: 20px;
	}
	.offcanvas__phone__num i {
		color: #353535;
		margin-right: 6px;
		font-size: 15px;
	}
	.offcanvas__phone__num span {
		font-size: 15px;
		color: #353535;
	}
	.blog__details__hero__text ul li {
		margin-right: 25px;
	}
	.blog__details__hero__text ul li span::after {
		right: -16px;
	}
}

/* Small Device = 320px */

@media only screen and (max-width: 479px) {
	.section-title h2,
	.breadcrumb__text h2 {
		font-size: 34px;
	}
	.hero__text__title h2 {
		font-size: 38px;
	}
	.hero__tab__form form .car-price .price-input {
		position: relative;
		left: 0;
		top: 0;
		padding-top: 15px;
	}
	.hero__tab__form {
		padding: 42px 20px 50px;
	}
	.hero__tab__form form .select-list .select-list-item {
		max-width: 100%;
		float: none;
		-webkit-box-flex: 100%;
		-ms-flex: 100%;
		flex: 100%;
		margin-right: 0;
	}
	.hero__tab__form form .select-list {
		margin-right: 0;
	}
	.hero__text__price {
		padding-left: 0;
	}
	.hero__text__price .car-model {
		position: relative;
	}
	.hero__text__price h2 {
		margin-top: 20px;
		font-size: 34px;
	}
	.feature__text {
		margin-bottom: 40px;
	}
	.feature:after {
		display: none;
	}
	.feature__text__btn .primary-btn {
		margin-bottom: 10px;
	}
	.blog__details__comment__item.reply__comment {
		margin-left: 0;
	}
	.hero__text .primary-btn {
		margin-bottom: 10px;
	}
	.filter__controls li {
		margin-right: 15px;
	}
	.blog__details__hero__text h2 {
		font-size: 35px;
		line-height: normal;
	}
	.blog__details__quote {
		padding: 0;
	}
	.blog__details__comment__item__pic {
		float: none;
		margin-bottom: 15px;
	}
	.blog__details__author__pic {
		float: none;
		margin-right: 0;
	}
	.blog__details__author__text {
		overflow: visible;
		padding-top: 30px;
	}
	.section-title.about-title h2 {
		line-height: normal;
	}
	.search-model-form input {
		font-size: 24px;
		width: 100%;
	}
}