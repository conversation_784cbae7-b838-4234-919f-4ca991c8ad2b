# تعليمات إعداد وتشغيل مشروع مزاد السيارات على Visual Studio 2022

## المتطلبات الأساسية

### 1. تثبيت Visual Studio 2022
- تحميل Visual Studio 2022 Community (مجاني) أو Professional
- تأكد من تثبيت المكونات التالية:
  - ASP.NET and web development workload
  - .NET Framework 4.8 targeting pack
  - SQL Server Express LocalDB

### 2. تثبيت SQL Server Express (إذا لم يكن مثبتاً)
- تحميل SQL Server Express من موقع Microsoft
- أو استخدام SQL Server LocalDB المدمج مع Visual Studio

## خطوات الإعداد

### الخطوة 1: فتح المشروع
1. افتح Visual Studio 2022
2. اختر "Open a project or solution"
3. انتقل إلى مجلد المشروع واختر `CarAuctionWebsite.sln`

### الخطوة 2: استعادة الحزم (Packages)
1. انقر بزر الماوس الأيمن على Solution في Solution Explorer
2. اختر "Restore NuGet Packages"
3. انتظر حتى تكتمل عملية التحميل

### الخطوة 3: إعداد قاعدة البيانات
1. تأكد من أن SQL Server Express يعمل
2. في Visual Studio، اذهب إلى View > Server Explorer
3. انقر بزر الماوس الأيمن على "Data Connections"
4. اختر "Add Connection"
5. اختر Microsoft SQL Server Database File
6. انتقل إلى مجلد `CarAuctionWebsite\App_Data` واختر `cars_DB.mdf`

### الخطوة 4: تحديث Connection String (إذا لزم الأمر)
إذا واجهت مشاكل في الاتصال بقاعدة البيانات:
1. افتح ملف `Web.config`
2. ابحث عن `<connectionStrings>`
3. قد تحتاج لتحديث Connection String حسب إعدادات SQL Server لديك

مثال لـ LocalDB:
```xml
<add name="CarConDB" 
     connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\cars_DB.mdf;Integrated Security=True" 
     providerName="System.Data.SqlClient" />
```

### الخطوة 5: تشغيل المشروع
1. اضغط F5 أو اختر "Start Debugging"
2. سيفتح المتصفح تلقائياً
3. ستظهر الصفحة الرئيسية للموقع

## استكشاف الأخطاء الشائعة

### خطأ في قاعدة البيانات
**المشكلة**: Cannot attach the file as database
**الحل**:
1. تأكد من تشغيل SQL Server Express
2. تأكد من وجود صلاحيات الكتابة في مجلد App_Data
3. جرب تشغيل Visual Studio كـ Administrator

### خطأ في ملفات CSS/JS
**المشكلة**: لا تظهر التنسيقات بشكل صحيح
**الحل**:
1. تأكد من وجود ملفات CSS في مجلد css
2. تأكد من وجود ملفات JavaScript في مجلد js
3. تحقق من المسارات في ملفات HTML

### خطأ في الصور
**المشكلة**: لا تظهر صور السيارات
**الحل**:
1. تأكد من وجود الصور في مجلد Car_Photo
2. تحقق من أسماء الملفات في قاعدة البيانات

### خطأ في التجميع (Compilation Error)
**المشكلة**: أخطاء في التجميع
**الحل**:
1. تأكد من تثبيت .NET Framework 4.8
2. نظف المشروع: Build > Clean Solution
3. أعد بناء المشروع: Build > Rebuild Solution

## اختبار المشروع

### 1. اختبار الصفحة الرئيسية
- تأكد من ظهور الصفحة الرئيسية بشكل صحيح
- تأكد من عمل الروابط

### 2. اختبار إنشاء حساب
- جرب إنشاء حساب جديد
- تأكد من ظهور رسائل التحقق

### 3. اختبار تسجيل الدخول
- جرب تسجيل الدخول بحساب موجود
- تأكد من التوجيه للصفحة الصحيحة

### 4. اختبار عرض السيارات
- تأكد من ظهور السيارات المتوفرة
- جرب البحث بالطرق المختلفة

## نصائح للتطوير

### 1. استخدام Debugger
- ضع نقاط توقف (Breakpoints) في الكود
- استخدم F10 للتنقل خطوة بخطوة
- راقب قيم المتغيرات في Watch window

### 2. فحص قاعدة البيانات
- استخدم Server Explorer لفحص البيانات
- تأكد من وجود بيانات تجريبية

### 3. فحص الأخطاء
- راجع Output window للأخطاء
- استخدم Browser Developer Tools لفحص أخطاء JavaScript

## إضافة بيانات تجريبية

إذا كانت قاعدة البيانات فارغة، يمكنك إضافة بيانات تجريبية:

### إضافة مستخدم إداري:
```sql
INSERT INTO TB_user (User_name, User_password) 
VALUES ('admin', '123456')
```

### إضافة عميل تجريبي:
```sql
INSERT INTO TB_customer (phone, city, region, address, near_point, customer_name, bank_name, branch, customer_type, password) 
VALUES ('**********', 'طرابلس', 'المركز', 'شارع الجمهورية', 'الساحة الخضراء', 'أحمد محمد', 'البنك الأهلي', 'الفرع الرئيسي', 'حساب شخصي', '123456')
```

## الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تأكد من اتباع جميع الخطوات بالترتيب
2. راجع ملف README.md للمزيد من التفاصيل
3. تأكد من تحديث Visual Studio إلى أحدث إصدار
4. تأكد من تثبيت جميع المتطلبات المذكورة أعلاه
