﻿Imports System.Data
Partial Class visitor_page_Car_details
    Inherits System.Web.UI.Page

    Dim obj_car As New Car_Class
    Dim obj_ac As New Auction_Class
    Dim obj_ph As New Photo_class
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim dt As DataTable = obj_car.select_car(Request.QueryString("car_id"))
        If dt.Rows.Count > 0 Then
            Label_gas.Text = dt.Rows(0).Item("gas_type")
            Label_color.Text = dt.Rows(0).Item("color")
            Label_Cylinders.Text = dt.Rows(0).Item("Cylinders")
            Label_distance.Text = dt.Rows(0).Item("distance_travele")
            Label_engine.Text = dt.Rows(0).Item("Engine_Type")
            Label_key.Text = dt.Rows(0).Item("keys")
            Label_notes.Text = dt.Rows(0).Item("notes")
            Label_id.Text = dt.Rows(0).Item("id")
            Label_type.Text = dt.Rows(0).Item("car_type")
            Label_year.Text = dt.Rows(0).Item("year_made")
            Label1_damages.Text = dt.Rows(0).Item("damages")
        End If
        Dim dt_ac As DataTable = obj_ac.ID_auction(Request.QueryString("car_id"))
        Dim dt2 As DataTable = obj_ac.select_auction(dt_ac.Rows(0).Item("auction_id"))
        If dt2.Rows.Count > 0 Then

            TextBox_start_date.Text = dt2.Rows(0).Item("start_date")
            TextBox_start_time.Text = dt2.Rows(0).Item("start_time").ToString
        End If
        Dim dt3 As DataTable = obj_ph.select_photo(Request.QueryString("car_id"))
        If dt3.Rows.Count > 0 Then
            Image_cover.ImageUrl = "~/Car_Photo/" + dt3.Rows(0).Item("Photo_name")
        End If
        Dim dt4 As DataTable = obj_ph.select_all_photo(Request.QueryString("car_id"))
        Dim c As Integer = 0
        If dt4.Rows.Count > 0 Then
            While c < dt4.Rows.Count
                If c = 0 Then
                    Image1.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 1 Then
                    Image2.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 2 Then
                    Image3.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 3 Then
                    Image4.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 4 Then
                    Image5.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")

                End If

                c = c + 1
            End While
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Response.Redirect("~/visitor_page/Auction_online.aspx?id=" + Request.QueryString("car_id"))
    End Sub
End Class
