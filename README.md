# مشروع موقع مزاد السيارات - Car Auction Website

## نظرة عامة
هذا المشروع عبارة عن موقع ويب لمزاد السيارات تم تحويله من VB.NET إلى C# ليعمل على Visual Studio 2022.

## المتطلبات
- Visual Studio 2022
- .NET Framework 4.8
- SQL Server Express LocalDB أو SQL Server

## التقنيات المستخدمة
- **Backend**: C#, ASP.NET Web Forms
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **Database**: SQL Server
- **Framework**: .NET Framework 4.8

## هيكل المشروع
```
CarAuctionWebsite/
├── App_Code/                 # كلاسات C# للعمليات
│   ├── CarClass.cs          # إدارة السيارات
│   ├── CustomerClass.cs     # إدارة العملاء
│   ├── AuctionClass.cs      # إدارة المزادات
│   ├── PaymentClass.cs      # إدارة المدفوعات
│   ├── PhotoClass.cs        # إدارة الصور
│   ├── UserClass.cs         # إدارة المستخدمين
│   └── BankClass.cs         # إدارة البنوك
├── App_Data/                # قاعدة البيانات
│   ├── cars_DB.mdf
│   └── cars_DB_log.LDF
├── visitor_page/            # صفحات الزوار
│   ├── home_page.aspx
│   └── home_page.aspx.cs
├── css/                     # ملفات التنسيق
├── js/                      # ملفات JavaScript
├── img/                     # الصور
├── fonts/                   # الخطوط
├── Car_Photo/              # صور السيارات
├── Default.aspx            # الصفحة الرئيسية
├── Global.asax             # إعدادات التطبيق
└── Web.config              # إعدادات الموقع
```

## خطوات التشغيل

### 1. فتح المشروع
1. افتح Visual Studio 2022
2. اختر "Open a project or solution"
3. اختر ملف `CarAuctionWebsite.sln`

### 2. إعداد قاعدة البيانات
1. تأكد من تشغيل SQL Server Express
2. قاعدة البيانات موجودة في مجلد `App_Data`
3. Connection String موجود في `Web.config`

### 3. تشغيل المشروع
1. اضغط F5 أو اختر "Start Debugging"
2. سيفتح المتصفح على الصفحة الرئيسية
3. انقر على "دخول الموقع" للوصول للصفحة الرئيسية

## الميزات الرئيسية
- **إنشاء حساب جديد**: للعملاء الجدد
- **تسجيل الدخول**: للعملاء والإداريين
- **عرض السيارات المتوفرة**: مع إمكانية البحث
- **المزاد على السيارات**: نظام مزاد مباشر
- **الشراء الفوري**: إمكانية الشراء بدون مزاد
- **إدارة المدفوعات**: نظام دفع متكامل

## البحث في السيارات
يمكن البحث عن السيارات بثلاث طرق:
1. **حسب النوع**: البحث بنوع السيارة
2. **حسب الموديل**: البحث بسنة الصنع
3. **حسب رقم التعريف**: البحث برقم السيارة

## قاعدة البيانات
تحتوي قاعدة البيانات على الجداول التالية:
- `TB_car`: بيانات السيارات
- `TB_customer`: بيانات العملاء
- `TB_user`: بيانات المستخدمين الإداريين
- `TB_Auction`: بيانات المزادات
- `TB_payment`: بيانات المدفوعات
- `TB_Photo`: صور السيارات
- `TB_bank`: بيانات البنوك

## ملاحظات مهمة
1. تأكد من تشغيل SQL Server Express قبل تشغيل المشروع
2. قد تحتاج لتحديث Connection String في Web.config حسب إعدادات SQL Server لديك
3. تأكد من وجود صلاحيات الكتابة في مجلد App_Data

## استكشاف الأخطاء
- إذا ظهرت أخطاء في قاعدة البيانات، تأكد من تشغيل SQL Server Express
- إذا لم تظهر الصور، تأكد من وجود الصور في مجلد Car_Photo
- إذا لم تعمل ملفات CSS، تأكد من المسارات في ملفات HTML

## التطوير المستقبلي
يمكن إضافة المزيد من الميزات مثل:
- نظام إشعارات
- تقييم السيارات
- تقارير مفصلة
- واجهة إدارية محسنة
- دعم للغات متعددة

## المطور
تم تحويل هذا المشروع من VB.NET إلى C# بواسطة Augment Agent
