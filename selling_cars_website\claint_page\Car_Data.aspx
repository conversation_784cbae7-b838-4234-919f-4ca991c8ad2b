﻿<%@ Page Title="" Language="VB" MasterPageFile="~/claint_page/Customer_Master.master" AutoEventWireup="false" CodeFile="Car_Data.aspx.vb" Inherits="claint_page_Car_Data" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6" dir="rtl">
                  
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" DataKeyNames="id" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px">
        <Columns>
            <asp:BoundField DataField="id" HeaderText="رقم  التعريف" InsertVisible="False" 
                ReadOnly="True" SortExpression="id" />
            <asp:BoundField DataField="year_made" HeaderText="الموديل" 
                SortExpression="year_made" />
            <asp:BoundField DataField="distance_travele" HeaderText="المسافة المقطوعة" 
                SortExpression="distance_travele" />
            <asp:BoundField DataField="car_type" HeaderText="نوع السياره" 
                SortExpression="car_type" />
            <asp:BoundField DataField="Engine_Type" HeaderText="نوع المحرك" 
                SortExpression="Engine_Type" />
            <asp:BoundField DataField="color" HeaderText="اللون" SortExpression="color" />
            <asp:BoundField DataField="gas_type" HeaderText="نوع الوقود" 
                SortExpression="gas_type" />
            <asp:BoundField DataField="location" HeaderText="الموقع" 
                SortExpression="location" />
            <asp:TemplateField>
                <ItemTemplate>
                    <asp:HyperLink ID="hyperlink1" runat="server" 
                        NavigateUrl='<%# Eval("id", "~/claint_page/Photo_car.aspx?car_id={0}") %>'>إرفاق صور</asp:HyperLink>
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
    </asp:GridView>
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        SelectCommand="SELECT id, distance_travele, damages, Cylinders, color, Engine_Type, car_type, year_made, gas_type, keys, location, notes, state_pay, add_by FROM TB_car WHERE (state_pay = @state_pay) AND (add_by = @add_by)">
                        <SelectParameters>
                            <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                            <asp:SessionParameter DefaultValue="" Name="add_by" 
                                SessionField="customer_phone" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                </div>
                <div class="col-lg-6 col-md-6" dir="rtl">
                    <div class="contact__form">
                        <div>
                            <div class="row" dir="rtl">
                                <div class="col-lg-6">
                                    
                                
                          <asp:TextBox ID="TextBox_travel" runat="server" class="form-control" placeholder="ادخل المسافة المقطوعة"></asp:TextBox>
                          </div> 
                                <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_damages" runat="server" class="form-control" placeholder="ادخل الاضرار" TextMode="MultiLine"></asp:TextBox>
                                <br/>
                                </div>
                                 <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_Cylinders" runat="server" class="form-control" placeholder="ادخل عدد الاسطوانات" MaxLength="1"></asp:TextBox>
                                <br/>
                                </div>
                                <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_color" runat="server" class="form-control" placeholder="ادخل لون السيارة" MaxLength="50"></asp:TextBox>
                               <br/>
                                </div>
                                 <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_Engine" runat="server" class="form-control" placeholder="ادخل نوع المحرك" MaxLength="50"></asp:TextBox>
                               <br/>
                                </div>
                                 <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_type" runat="server" class="form-control" placeholder="ادخل نوع السيارة" MaxLength="50"></asp:TextBox>
                                <br/>
                                </div>
                                 <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_year" runat="server" class="form-control" placeholder="ادخل موديل السيارة" MaxLength="4"></asp:TextBox>
                                <br/>
                                </div>

                              
                                <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_keys" runat="server" class="form-control" placeholder="ادخل عدد المفاتيح" MaxLength="1"></asp:TextBox>
                               <br/>
                                </div>
                                <div class="col-lg-6">
                                            
                          <asp:TextBox ID="TextBox_location" runat="server" class="form-control" placeholder="ادخل موقع السيارة" MaxLength="50"></asp:TextBox>
                                <br/>
                                </div>
                                <div class="col-lg-6" textmode="MultiLine">
                                            
                          <asp:TextBox ID="TextBox_note" runat="server" class="form-control" placeholder="ادخل الملاحظات"></asp:TextBox>
                              <br/>
                                </div>
                                 <div class="col-lg-6">     
                          <asp:TextBox ID="TextBox_value_ac" runat="server" class="form-control" placeholder="قيمة بداية المزاد"  TextMode="SingleLine"></asp:TextBox>
                                <br/>
                                </div> 
                                 <div class="col-lg-6">                                  
                          <asp:TextBox ID="TextBox_buy" runat="server" class="form-control" placeholder="قيمة الشراء الفوري"  TextMode="SingleLine"></asp:TextBox>
                                <br/>
                                </div>
                                <asp:DropDownList ID="DropDownList_gas" runat="server" class="form-control">
                                  <asp:ListItem>اختر نوع الوقود</asp:ListItem>
                                  <asp:ListItem>بنزينة</asp:ListItem>
                                  <asp:ListItem>نافتة</asp:ListItem>

                                </asp:DropDownList>
                            </div>
                          <br />
                          
                         
                    <asp:Button ID="Button1" runat="server" class="site-btn" Text="نشر بيانات المركبة" />
                           <br />
                        <asp:Label ID="Label1" runat="server" Text="Label" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</asp:Content>

