﻿
Partial Class claint_page_Show_Cars
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If RadioButton_id.Checked = False And RadioButton_type.Checked = False And RadioButton_year.Checked = False Then
            DataList1.Visible = True
            DataList1.DataBind()
        Else
            DataList1.Visible = False
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        If RadioButton_type.Checked = True Then
            DataList3.Visible = True
            DataList3.DataBind()
        Else
            DataList3.Visible = False
        End If
        If RadioButton_year.Checked = True Then
            DataList4.Visible = True
            DataList4.DataBind()
        Else
            DataList4.Visible = False
        End If
        If RadioButton_id.Checked = True Then
            DataList5.Visible = True
            DataList5.DataBind()
        Else
            DataList5.Visible = False
        End If
    End Sub
End Class
