﻿Imports System.Data.SqlClient
Imports System.Data
Imports Microsoft.VisualBasic

Public Class Payment_Class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Sub add_payment(ByVal phone, ByVal auction_id, ByVal max_value, ByVal sub_date, ByVal sub_time, ByVal pay_state)
        Dim cmd As New SqlCommand("insert into TB_payment ( phone,  auction_id, max_value,  sub_date,  sub_time,   pay_state)values(@phone, @auction_id, @max_value,  @sub_date,  @sub_time,  @pay_state)", con)
        cmd.Parameters.AddWithValue("@phone", phone)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        cmd.Parameters.AddWithValue("@max_value", max_value)
        cmd.Parameters.AddWithValue("@sub_date", sub_date)
        cmd.Parameters.AddWithValue("@sub_time", sub_time)
        cmd.Parameters.AddWithValue("@pay_state", pay_state)

        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub

    Function check_payment(ByVal phone, ByVal auction_id)
        Dim cmd As New SqlCommand("select * from TB_payment where phone=@phone and  auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@phone", phone)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt


    End Function

    Sub update_payment(ByVal phone, ByVal auction_id, ByVal max_value, ByVal sub_date, ByVal sub_time)
        Dim cmd As New SqlCommand("update TB_payment set max_value=@max_value,sub_date=@sub_date,sub_time=@sub_time  where phone=@phone and  auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@phone", phone)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        cmd.Parameters.AddWithValue("@max_value", max_value)
        cmd.Parameters.AddWithValue("@sub_date", sub_date)
        cmd.Parameters.AddWithValue("@sub_time", sub_time)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub

    Function check_max_value(ByVal auction_id)
        Dim cmd As New SqlCommand("select max_value ,phone from TB_payment where auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Dim c As Integer = 0
        Dim max As Double = 0
        Dim ph As String = ""
        If dt.Rows.Count > 0 Then
            While c < dt.Rows.Count
                If max < dt.Rows(c).Item("max_value") Then
                    max = dt.Rows(c).Item("max_value")
                    ph = dt.Rows(c).Item("phone")
                End If
                c = c + 1
            End While

        End If
        Dim dt_new As New DataTable
        Dim c1 = New DataColumn("max_value", GetType(Double))
        Dim c2 = New DataColumn("phone", GetType(String))
        dt_new.Columns.Add(c1)
        dt_new.Columns.Add(c2)
        dt_new.Rows.Add(max, ph)
        Return dt_new
    End Function


    Sub check_out(ByVal phone, ByVal auction_id, ByVal pay_way, ByVal shipping_way)
        Dim cmd As New SqlCommand("update TB_payment set pay_way=@pay_way,shipping_way=@shipping_way,pay_state='true'  where phone=@phone and  auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@phone", phone)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        cmd.Parameters.AddWithValue("@pay_way", pay_way)
        cmd.Parameters.AddWithValue("@shipping_way", shipping_way)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub

    Function check_customer_pay(ByVal auction_id)
        Dim cmd As New SqlCommand("select *  from TB_payment where auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Dim c As Integer = 0
        Dim max As Double = 0
        Dim state As Boolean
        Dim phone As String = ""
        Dim sub_date As Date
        Dim sub_time As TimeSpan
        Dim pay_way As String = ""
        Dim shipping_way As String = ""
        Dim dt_new As New DataTable
        If dt.Rows.Count > 0 Then
            While c < dt.Rows.Count
                If max < dt.Rows(c).Item("max_value") Then
                    max = dt.Rows(c).Item("max_value")
                    state = dt.Rows(c).Item("pay_state")
                    phone = dt.Rows(c).Item("phone")
                    sub_date = dt.Rows(c).Item("sub_date")
                    sub_time = dt.Rows(c).Item("sub_time")
                    pay_way = dt.Rows(c).Item("pay_way").ToString
                    shipping_way = dt.Rows(c).Item("shipping_way").ToString

                End If
                c = c + 1
            End While


            Dim c1 = New DataColumn("max_value", GetType(Double))
            Dim c2 = New DataColumn("phone", GetType(String))
            Dim c3 = New DataColumn("sub_date", GetType(Date))
            Dim c4 = New DataColumn("sub_time", GetType(TimeSpan))
            Dim c5 = New DataColumn("pay_way", GetType(String))
            Dim c6 = New DataColumn("shipping_way", GetType(String))
            dt_new.Columns.Add(c1)
            dt_new.Columns.Add(c2)
            dt_new.Columns.Add(c3)
            dt_new.Columns.Add(c4)
            dt_new.Columns.Add(c5)
            dt_new.Columns.Add(c6)
            If state = False Then
                dt_new.Rows.Add(max, phone, sub_date, sub_time, pay_way, shipping_way)
            
            End If


        End If

        Return dt_new
    End Function
End Class
