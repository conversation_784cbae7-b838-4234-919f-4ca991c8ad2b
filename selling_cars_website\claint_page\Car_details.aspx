﻿<%@ Page Title="" Language="VB" MasterPageFile="~/claint_page/Customer_Master.master" AutoEventWireup="false" CodeFile="Car_details.aspx.vb" Inherits="claint_page_Car_details" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <div class="container">
            <div class="row">
                <div class="col-lg-9">
                    <div class="car__details__pic">
                        <div class="car__details__pic__large" style="text-align: right">
                            
                          <asp:Image ID="Image_cover" runat="server" class="car-big-img"></asp:Image>
                        </div>
                        <div class="car-thumbs">
                            <div class="car-thumbs-track car__thumb__slider owl-carousel">
                                <div class="ct"><asp:Image ID="Image1" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image2" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image3" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image4" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image5" runat="server"></asp:Image></div>
                            </div>
                        </div>
                    </div>
                    <div class="car__details__tab">
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#tabs-1" role="tab">الاضرار</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#tabs-2" role="tab">الملاحظات</a>
                            </li>
                            
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tabs-1" role="tabpanel">
                                <div class="car__details__tab__info">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12">
                                            <div class="car__details__tab__info__item">
                                            <p >
                                            
                                            
                                           <asp:Label ID="Label1_damages" runat="server" Text="Label"></asp:Label>
                                            
                                            </p>   
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                                -
                            </div>
                            <div class="tab-pane" id="tabs-2" role="tabpanel">
                                <div class="car__details__tab__info">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12">
                                            <div class="car__details__tab__info__item">
                                                <p>
                                                
                                      <asp:Label ID="Label_notes" runat="server" Text="Label"></asp:Label>
                                            
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="col-lg-3" dir="rtl" >
                    <div class="car__details__sidebar">
                        <div class="car__details__sidebar__model">
                            <ul>
                                <li><table><tr> <td> رقم التعريف</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_id" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> نوع المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_type" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> موديل المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_year" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> لون المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_color" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                            </ul>
                            <h5 align="right">تاريخ بداية المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_start_date" runat="server" class="form-control" ReadOnly="True"></asp:TextBox></p> 
                             <h5 align="right">تاريخ نهاية المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_end_y" runat="server" class="form-control" ReadOnly="True"></asp:TextBox></p>    
                             <h5 align="right">توقيت بداية المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_start_time" runat="server" class="form-control" TextMode="Time" ReadOnly="True"></asp:TextBox></p>
                            <h5 align="right">توقيت نهاية المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_end_t" runat="server" class="form-control" TextMode="Time" ReadOnly="True"></asp:TextBox></p>  
                        </div>
                        <div class="car__details__sidebar__payment">
                            <ul>
                               <li><table><tr> <td> نوع المحرك</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_engine" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> المفتاح</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_key" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                              <li><table><tr> <td> الاسطوانات</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_Cylinders" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> نوع الوقود</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_gas" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> المسافة المقطوعة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_distance" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                           <li><table><tr> <td> قيمة بداية المزاد</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_start_v" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                             <li><table><tr> <td> قيمة الشراء الفوري</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_buy" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                            </ul>
                             <asp:LinkButton ID="LinkButton1" runat="server" class="primary-btn"> تقديم مزاد </asp:LinkButton>
                             <br />     
                             <asp:LinkButton ID="LinkButton2" runat="server" class="primary-btn"> شراء فوري </asp:LinkButton>           
                        </div>
                    </div>
                </div>
                <div class="col-lg-9" dir="rtl">
                    <div class="car__details__pic">
                        <div class="car__details__pic__large">
                        <h3>مشاركات الزبائن</h3>
                        <br />
                           <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px" 
                        AllowPaging="True">
                   <Columns>
                       <asp:BoundField DataField="customer_name" HeaderText="الزبون" 
                           SortExpression="customer_name" />
                       <asp:BoundField DataField="max_value" HeaderText="القيمة" 
                           SortExpression="max_value" />
                       <asp:BoundField DataField="sub_date" HeaderText="التاريخ" 
                           SortExpression="sub_date" DataFormatString="{0:d}" />
                       <asp:BoundField DataField="sub_time" HeaderText="الوقت" 
                           SortExpression="sub_time" />
                   </Columns>
                    </asp:GridView>
                    
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        SelectCommand="SELECT TB_customer.customer_name, TB_payment.max_value, TB_payment.sub_date, TB_payment.sub_time, TB_payment.auction_id FROM TB_customer INNER JOIN TB_payment ON TB_customer.phone = TB_payment.phone WHERE (TB_payment.auction_id = @auction_id) ORDER BY TB_payment.max_value DESC">
                        <SelectParameters>
                            <asp:SessionParameter Name="auction_id" SessionField="auction_number" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                    
                        </div> 
                        </div> 
                        </div> 
            </div>
        </div>
</asp:Content>

