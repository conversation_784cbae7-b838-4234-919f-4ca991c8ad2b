<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test_db.aspx.cs" Inherits="CarAuctionWebsite.test_db" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Database Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>Database Connection Test - اختبار الاتصال بقاعدة البيانات</h1>
        
        <asp:Button ID="btnTest" runat="server" Text="Test Database Connection" OnClick="btnTest_Click" />
        <br /><br />
        
        <asp:Label ID="lblResult" runat="server"></asp:Label>
        
        <asp:GridView ID="gvTables" runat="server" AutoGenerateColumns="true" Visible="false">
        </asp:GridView>
        
        <div style="margin-top: 30px;">
            <h3>معلومات مهمة:</h3>
            <ul>
                <li>تأكد من تشغيل SQL Server Express</li>
                <li>تأكد من وجود ملف قاعدة البيانات في مجلد App_Data</li>
                <li>تأكد من صحة Connection String في Web.config</li>
            </ul>
        </div>
    </form>
</body>
</html>
