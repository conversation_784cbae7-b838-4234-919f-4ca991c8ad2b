# دليل حل المشاكل - Troubleshooting Guide

## 🔧 المشاكل التي تم إصلاحها

### 1. مشكلة namespace في home_page.aspx.cs
**المشكلة**: خطأ في namespace للصفحة
**الحل**: تم تحديث namespace إلى `CarAuctionWebsite.visitor_page`

### 2. مشكلة DataList في Page_Load
**المشكلة**: محاولة الوصول لـ DataList غير موجودة
**الحل**: تم إضافة فحص null وتحسين منطق Page_Load

### 3. مشكلة ShowMessage method
**المشكلة**: استخدام خاطئ لـ Label.CreateLabel()
**الحل**: تم إصلاح method signature وإضافة null check

### 4. مشكلة الروابط المكسورة
**المشكلة**: روابط تشير لصفحات غير موجودة
**الحل**: تم تعطيل الروابط مؤقتاً وإضافة رسائل نجاح

### 5. مشكلة using statements
**المشكلة**: نقص في using statements
**الحل**: تم إضافة `using System.Web.UI.WebControls;`

## 🚀 كيفية اختبار المشروع

### الخطوة 1: اختبار بسيط
1. افتح المشروع في Visual Studio 2022
2. اضغط F5 لتشغيل المشروع
3. انقر على "اختبار بسيط"
4. اختبر الكلاسات وقاعدة البيانات

### الخطوة 2: اختبار قاعدة البيانات
1. انقر على "اختبار قاعدة البيانات"
2. تأكد من ظهور رسالة نجاح الاتصال
3. إذا فشل، راجع الحلول أدناه

### الخطوة 3: اختبار الصفحة الرئيسية
1. انقر على "دخول الموقع"
2. جرب إنشاء حساب جديد
3. جرب تسجيل الدخول

## ❌ المشاكل الشائعة والحلول

### مشكلة: خطأ في قاعدة البيانات
```
Cannot attach the file as database
```
**الحلول:**
1. تأكد من تشغيل SQL Server Express:
   ```
   services.msc → SQL Server (SQLEXPRESS)
   ```
2. تشغيل Visual Studio كـ Administrator
3. تحديث Connection String في Web.config:
   ```xml
   <add name="CarConDB" 
        connectionString="Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\cars_DB.mdf;Integrated Security=True" 
        providerName="System.Data.SqlClient" />
   ```

### مشكلة: ملفات CSS لا تظهر
**الحلول:**
1. تأكد من وجود ملفات CSS في مجلد css
2. تحقق من المسارات في home_page.aspx
3. تأكد من إعدادات IIS Express

### مشكلة: خطأ في التجميع (Compilation Error)
**الحلول:**
1. Clean Solution: `Build → Clean Solution`
2. Rebuild Solution: `Build → Rebuild Solution`
3. تأكد من تثبيت .NET Framework 4.8
4. تحقق من صحة using statements

### مشكلة: صفحة فارغة أو خطأ 500
**الحلول:**
1. تحقق من ملف Web.config
2. تأكد من وجود Global.asax
3. فحص Event Viewer للأخطاء
4. تفعيل customErrors في Web.config:
   ```xml
   <customErrors mode="Off" />
   ```

## 🔍 أدوات التشخيص

### 1. صفحة الاختبار البسيط
- URL: `http://localhost:port/test_simple.aspx`
- تختبر: الكلاسات، قاعدة البيانات، إنشاء عميل

### 2. صفحة اختبار قاعدة البيانات
- URL: `http://localhost:port/test_db.aspx`
- تختبر: الاتصال، الجداول، البيانات

### 3. فحص الأخطاء في Visual Studio
- Output Window: `View → Output`
- Error List: `View → Error List`
- Debug Console: أثناء التشغيل

## 📝 نصائح للتطوير

### 1. استخدام Breakpoints
```csharp
// ضع breakpoint هنا لفحص البيانات
var result = customerClass.Login(username);
```

### 2. فحص قاعدة البيانات
- Server Explorer في Visual Studio
- SQL Server Management Studio
- Visual Studio Database Tools

### 3. فحص الشبكة
- Browser Developer Tools (F12)
- Network tab لفحص طلبات CSS/JS
- Console tab لفحص أخطاء JavaScript

## 🆘 إذا لم تنجح الحلول

### 1. إعادة إنشاء قاعدة البيانات
1. احذف ملفات .mdf و .ldf من App_Data
2. أنشئ قاعدة بيانات جديدة
3. استخدم sample_data.sql لإدراج البيانات

### 2. إعادة إنشاء المشروع
1. أنشئ مشروع ASP.NET جديد
2. انسخ الملفات واحداً تلو الآخر
3. اختبر كل ملف بعد إضافته

### 3. فحص متطلبات النظام
- Windows 10/11
- Visual Studio 2022
- .NET Framework 4.8
- SQL Server Express
- IIS Express

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم:
1. إصدار Visual Studio
2. إصدار Windows
3. رسالة الخطأ الكاملة
4. خطوات إعادة إنتاج المشكلة
5. لقطة شاشة للخطأ

### ملفات مهمة للفحص:
- `Web.config`
- `Global.asax.cs`
- `CarAuctionWebsite.csproj`
- ملفات الكلاسات في App_Code

---

**ملاحظة**: تم إصلاح جميع المشاكل الأساسية في الكود. المشروع جاهز للتشغيل والاختبار!
