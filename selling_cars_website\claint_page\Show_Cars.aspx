﻿<%@ Page Title="" Language="VB" MasterPageFile="~/claint_page/Customer_Master.master" AutoEventWireup="false" CodeFile="Show_Cars.aspx.vb" Inherits="claint_page_Show_Cars" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">

    <!-- Car Section Begin -->
    <section class="car spad"id="avalible car">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>تصفح المركبات</span>
                        <h2>المركبات المتوفرة</h2>
                    </div>
                    <table align="center" dir="rtl">
                    <tr>
                   <td> <asp:TextBox ID="TextBox_type" runat="server" class= "form-control" placeholder="أدخل نوع المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_type" runat="server" Text="البحث حسب النوع" GroupName="s"></asp:RadioButton></td> 
                   <td> <asp:TextBox ID="TextBox_year" runat="server" class= "form-control" placeholder="أدخل موديل المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_year" runat="server" Text="البحث حسب الموديل" GroupName="s"></asp:RadioButton></td> 
                   <td> <asp:TextBox ID="TextBox_id" runat="server" class= "form-control" placeholder="أدخل تعريف المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_id" runat="server" Text="البحث حسب رقم التعريف" GroupName="s"></asp:RadioButton></td> 
                   <td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:LinkButton ID="LinkButton1" runat="server" class="primary-btn"> عرض المركبات </asp:LinkButton> 
                    <h6>  &nbsp;&nbsp;&nbsp;</h6>
                    </td>
                    
                    
                    </tr>





                    </table>
                </div>
            </div>
 <asp:DataList ID="DataList1" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource1" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                </SelectParameters> 
            </asp:SqlDataSource>
            

            <!--  datalist for car by type -->
            
 <asp:DataList ID="DataList3" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource3" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource3" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.car_type = @car_type)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_type" DefaultValue="" Name="car_type" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>


            
            <!--  datalist for car by year -->
            
 <asp:DataList ID="DataList4" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource4" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource4" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.year_made = @year_made)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_year" DefaultValue="" Name="year_made" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>


               
            <!--  datalist for car by id -->
            
 <asp:DataList ID="DataList5" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource5" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource5" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.id = @id)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_id" DefaultValue="" Name="id" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>
            
            
        </div>
    </section>
    <!-- Car Section End -->
</asp:Content>

