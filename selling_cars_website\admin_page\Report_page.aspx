﻿<%@ Page Title="" Language="VB" MasterPageFile="~/admin_page/Admin_Master.master" AutoEventWireup="false" CodeFile="Report_page.aspx.vb" Inherits="admin_page_Report_page" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="container">
            <div class="row">
                <div class="col-lg-12 col-md-12" dir="rtl">
                  <h3 align="center"> المركبات التي تم انهاء إجرائات دفعها</h3>
                  <br />

    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" DataKeyNames="phone" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="1000px" 
                        AllowPaging="True">
        <Columns>
            <asp:BoundField DataField="customer_name" HeaderText="الزبون" 
                SortExpression="customer_name" />
            <asp:BoundField DataField="phone" HeaderText="رقم الهاتف" 
                ReadOnly="True" SortExpression="phone" />
            <asp:BoundField DataField="city" HeaderText="المدينة" 
                SortExpression="city" />
            <asp:BoundField DataField="region" HeaderText="المنطقة" 
                SortExpression="region" />
            <asp:BoundField DataField="bank_name" HeaderText="المصرف" 
                SortExpression="bank_name" />
            <asp:BoundField DataField="branch" HeaderText="الفرع" 
                SortExpression="branch" />
            <asp:BoundField DataField="customer_type" HeaderText="نوع الزبون" 
                SortExpression="customer_type" />
            <asp:BoundField DataField="max_value" HeaderText="قيمة الشراء" 
                SortExpression="max_value" />
            <asp:BoundField DataField="sub_date" HeaderText="تاريخ الشراء" 
                SortExpression="sub_date" DataFormatString="{0:d}" />
            <asp:BoundField DataField="sub_time" HeaderText="توقيت الشراء" 
                SortExpression="sub_time" />
            <asp:BoundField DataField="pay_way" HeaderText="طريقة الدفع" 
                SortExpression="pay_way" />
            <asp:BoundField DataField="shipping_way" HeaderText="طريقة الشحن" 
                SortExpression="shipping_way" />
            <asp:BoundField DataField="id" HeaderText="رقم تعريف المركبة" 
                SortExpression="id" InsertVisible="False" />
        </Columns>
    </asp:GridView>
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        
                        SelectCommand="SELECT TB_customer.phone, TB_customer.city, TB_customer.region, TB_customer.customer_name, TB_customer.bank_name, TB_customer.branch, TB_customer.customer_type, TB_payment.max_value, TB_payment.sub_date, TB_payment.sub_time, TB_payment.pay_way, TB_payment.shipping_way, TB_payment.pay_state, TB_Auction.penal_clause, TB_car.car_type AS Expr1, TB_car.color AS Expr2, TB_car.year_made AS Expr3, TB_car.state_pay AS Expr4, TB_car.id FROM TB_customer INNER JOIN TB_payment ON TB_customer.phone = TB_payment.phone INNER JOIN TB_Auction ON TB_payment.auction_id = TB_Auction.auction_id INNER JOIN TB_car ON TB_Auction.id = TB_car.id WHERE (TB_payment.pay_state = @pay_state) AND (TB_car.state_pay = @state_pay)">
                        <SelectParameters>
                            <asp:Parameter DefaultValue="True" Name="pay_state" />
                            <asp:Parameter DefaultValue="True" Name="state_pay" Type="Boolean" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                </div>
                
                <br />

                <div class="col-lg-12 col-md-12" dir="rtl">
                  <h3 align="center"> المركبات التي لم يتم انهاء إجرائات دفعها</h3>
                  <br />

    <asp:GridView ID="GridView2" runat="server"  GridLines="None" Width="1200px" 
                        AllowPaging="True">
        
    </asp:GridView>
                    
                </div>
            </div>
        </div>
</asp:Content>

