/*---------------------
  Header
-----------------------*/
.header__top {
    background: $heading-color-2;
}

.header__top__widget {
    padding: 12px 0 14px;

    li {
        font-size: 15px;
        color: $white-color;
        display: inline-block;
        margin-right: 35px;
        list-style: none;

        &:last-child {
            margin-right: 0;
        }

        i {
            font-size: 18px;
            color: #8d8d8d;
            margin-right: 6px;
        }
    }
}

.header__top__right {
    text-align: right;
}

.header__top__phone {
    display: inline-block;
    margin-right: 20px;
    padding: 14px 0 12px;

    i {
        color: #8d8d8d;
        margin-right: 6px;
        font-size: 15px;
    }

    span {
        font-size: 15px;
        color: $white-color;
    }
}

.header__top__social {
    display: inline-block;
    padding: 14px 0 12px;

    a {
        display: inline-block;
        font-size: 15px;
        color: $white-color;
        margin-right: 16px;

        &:last-child {
            margin-right: 0;
        }
    }
}

.header__nav {
    text-align: right;
    padding: 32px 0 33px;
}

.header__logo {
    padding: 35px 0;

    a {
        display: inline-block;
    }
}

.header__menu {
    display: inline-block;

    ul {

        li {
            list-style: none;
            display: inline-block;
            margin-right: 60px;
            position: relative;

            &.active {

                a {

                    &:after {
                        width: 100%;
                        opacity: 1;
                    }
                }
            }

            &:hover {

                a {

                    &:after {
                        width: 100%;
                        opacity: 1;
                    }
                }

                .dropdown {
                    top: 32px;
                    opacity: 1;
                    visibility: visible;
                }
            }

            .dropdown {
                position: absolute;
                left: 0;
                top: 56px;
                width: 180px;
                background: $primary-color;
                text-align: left;
                padding: 2px 0;
                z-index: 9;
                opacity: 0;
                visibility: hidden;
                @include transition(all, .5s);

                li {
                    display: block;
                    margin-right: 0;

                    a {
                        font-size: 14px;
                        color: $white-color;
                        font-weight: 400;
                        padding: 8px 20px;
                        text-transform: capitalize;

                        &:after {
                            display: none;
                        }
                    }
                }
            }

            a {
                font-size: 15px;
                color: $heading-color;
                font-weight: 700;
                display: block;
                padding: 5px 0;
                text-transform: uppercase;
                position: relative;

                &:after {
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    height: 2px;
                    width: 0%;
                    background: $primary-color;
                    content: '';
                    opacity: 0;
                    @include transition(all, .5s);
                }
            }
        }
    }
}

.header__nav__widget {
    display: inline-block;

    .header__nav__widget__btn {
        display: inline-block;

        a {
            font-size: 15px;
            color: $heading-color;
            margin-right: 20px;
            display: inline-block;
        }
    }
}

.offcanvas-menu-wrapper {
    display: none;
}

.canvas__open {
    display: none;
}