using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.UI;

namespace CarAuctionWebsite
{
    public partial class test_db : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lblResult.Text = "انقر على الزر لاختبار الاتصال بقاعدة البيانات";
                lblResult.CssClass = "info";
            }
        }

        protected void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString;
                lblResult.Text = $"<strong>Connection String:</strong><br/>{connectionString}<br/><br/>";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    lblResult.Text += "<span class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح!</span><br/><br/>";

                    // Test if tables exist
                    string query = @"SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
                    
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            DataTable tables = new DataTable();
                            adapter.Fill(tables);

                            if (tables.Rows.Count > 0)
                            {
                                lblResult.Text += $"<span class='success'>✓ تم العثور على {tables.Rows.Count} جدول في قاعدة البيانات</span><br/>";
                                lblResult.Text += "<strong>الجداول الموجودة:</strong><br/>";
                                
                                foreach (DataRow row in tables.Rows)
                                {
                                    lblResult.Text += $"• {row["TABLE_NAME"]}<br/>";
                                }

                                gvTables.DataSource = tables;
                                gvTables.DataBind();
                                gvTables.Visible = true;
                            }
                            else
                            {
                                lblResult.Text += "<span class='error'>⚠ لم يتم العثور على أي جداول في قاعدة البيانات</span><br/>";
                            }
                        }
                    }

                    // Test specific tables
                    TestTable(connection, "TB_car", "السيارات");
                    TestTable(connection, "TB_customer", "العملاء");
                    TestTable(connection, "TB_user", "المستخدمين");
                    TestTable(connection, "TB_Auction", "المزادات");
                    TestTable(connection, "TB_payment", "المدفوعات");
                    TestTable(connection, "TB_Photo", "الصور");
                    TestTable(connection, "TB_bank", "البنوك");
                }
            }
            catch (Exception ex)
            {
                lblResult.Text = $"<span class='error'>✗ خطأ في الاتصال بقاعدة البيانات:</span><br/>";
                lblResult.Text += $"<strong>نوع الخطأ:</strong> {ex.GetType().Name}<br/>";
                lblResult.Text += $"<strong>رسالة الخطأ:</strong> {ex.Message}<br/><br/>";
                
                if (ex.InnerException != null)
                {
                    lblResult.Text += $"<strong>تفاصيل إضافية:</strong> {ex.InnerException.Message}<br/><br/>";
                }

                lblResult.Text += "<strong>الحلول المقترحة:</strong><br/>";
                lblResult.Text += "• تأكد من تشغيل SQL Server Express<br/>";
                lblResult.Text += "• تأكد من وجود ملف cars_DB.mdf في مجلد App_Data<br/>";
                lblResult.Text += "• تأكد من صحة Connection String في Web.config<br/>";
                lblResult.Text += "• جرب تشغيل Visual Studio كـ Administrator<br/>";
            }
        }

        private void TestTable(SqlConnection connection, string tableName, string arabicName)
        {
            try
            {
                string query = $"SELECT COUNT(*) FROM {tableName}";
                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    int count = (int)command.ExecuteScalar();
                    lblResult.Text += $"<span class='success'>✓ جدول {arabicName} ({tableName}): {count} سجل</span><br/>";
                }
            }
            catch (Exception ex)
            {
                lblResult.Text += $"<span class='error'>✗ خطأ في جدول {arabicName} ({tableName}): {ex.Message}</span><br/>";
            }
        }
    }
}
