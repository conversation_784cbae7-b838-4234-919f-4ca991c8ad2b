﻿Imports System.Data

Partial Class claint_page_buy_page
    Inherits System.Web.UI.Page
    Dim obj As New Payment_Class
    Dim obj_a As New Auction_Class
    Dim obj_b As New bank_class
    Dim obj_car As New Car_Class
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        Dim dt_car As DataTable = obj_car.select_car(Request.QueryString("id"))
        If dt_car.Rows.Count > 0 Then
            Session("buy_value") = dt_car.Rows(0).Item("buy_now")
            Session("id") = Request.QueryString("id")
        End If
    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim dt As DataTable = obj_b.check_account(TextBox_account.Text)
        If dt.Rows.Count = 0 Then

            Label2.Visible = True
            Label2.ForeColor = Drawing.Color.Red
            Label2.Text = "يوجد خطأ في رقم الحساب المصرفي المرسل"
            Exit Sub
        Else
            If dt.Rows(0).Item("totail") < Session("buy_value") Then
                Label2.Visible = True
                Label2.ForeColor = Drawing.Color.Red
                Label2.Text = "لا تتوفر القيمةالمراد دفعها في حسابك"
                Exit Sub
            Else
                obj_car.update_state(Session("id"))
                obj_b.sub_value(TextBox_account.Text, Session("buy_value"))
                obj_a.add_auction(Request.QueryString("id"), "", "", "", "", 0)
                Dim max As Integer = obj_a.max_auction()

                Session("ac_id") = max
               
            End If
            obj.add_payment(Session("customer_phone"), Request.QueryString("id"), Session("buy_value"), Date.Today, Date.Now.TimeOfDay, False)
            obj.check_out(Session("customer_phone"), Request.QueryString("id"), TextBox_pay.Text, TextBox_shipping.Text)
            Label2.Visible = True
            Label2.ForeColor = Drawing.Color.Green
            Label2.Text = "تم تأكيد الشراء وخصم القيمة من الحساب المصرفي"
        End If




    End Sub
End Class
