using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class CustomerClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void CreateAccount(string phone, string city, string region, string address, 
                                 string nearPoint, string customerName, string bankName, 
                                 string branch, string customerType, string password)
        {
            string query = @"INSERT INTO TB_customer (phone, city, region, address, near_point, 
                            customer_name, bank_name, branch, customer_type, password) 
                            VALUES (@phone, @city, @region, @address, @near_point, 
                            @customer_name, @bank_name, @branch, @customer_type, @password)";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@phone", phone);
            cmd.Parameters.AddWithValue("@city", city);
            cmd.Parameters.AddWithValue("@region", region);
            cmd.Parameters.AddWithValue("@address", address);
            cmd.Parameters.AddWithValue("@near_point", nearPoint);
            cmd.Parameters.AddWithValue("@customer_name", customerName);
            cmd.Parameters.AddWithValue("@bank_name", bankName);
            cmd.Parameters.AddWithValue("@branch", branch);
            cmd.Parameters.AddWithValue("@customer_type", customerType);
            cmd.Parameters.AddWithValue("@password", password);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable Login(string customerName)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_customer WHERE customer_name=@customer_name", con);
            cmd.Parameters.AddWithValue("@customer_name", customerName);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }
    }
}
