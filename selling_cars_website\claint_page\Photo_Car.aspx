﻿<%@ Page Title="" Language="VB" MasterPageFile="~/claint_page/Customer_Master.master" AutoEventWireup="false" CodeFile="Photo_Car.aspx.vb" Inherits="claint_page_Photo_Car" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6" dir="rtl">
                  <h3>   الصور المضافه مسبقا للمركبة</h3>
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" DataKeyNames="Photo_name" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px" 
                        AllowPaging="True" PageSize="3">
        <Columns>
            <asp:TemplateField>
                <ItemTemplate>
                    <asp:Image ID="Image1" runat="server" Height="300px" 
                        ImageUrl='<%# Eval("Photo_name","~/Car_Photo/{0}") %>' Width="300px" />
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
    </asp:GridView>
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        SelectCommand="SELECT * FROM [TB_Photo] WHERE ([Id] = @Id)">
                        <SelectParameters>
                            <asp:QueryStringParameter Name="Id" QueryStringField="car_id" Type="Int32" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                </div>
                <div class="col-lg-6 col-md-6" dir="rtl">
                    <div class="contact__form">
                        <div>
                            <h4 align="right">حمل صورة المركبة  </h4>
                          <br />
                          
                        <asp:FileUpload ID="FileUpload1" runat="server" class="form-control"  />
                             <br />
                                 <br />
                    <asp:Button ID="Button1" runat="server" class="site-btn" Text="إرفاق الصوره للمركبة" />
                           <br />
                        <asp:Label ID="Label1" runat="server" Text="Label" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</asp:Content>

