﻿<%@ Page Title="" Language="VB" MasterPageFile="~/admin_page/Admin_Master.master" AutoEventWireup="false" CodeFile="Payment_Date.aspx.vb" Inherits="admin_page_Payment_Date" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-6" dir="rtl">
                  
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" DataKeyNames="auction_id" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px">
        <Columns>
            <asp:BoundField DataField="id" HeaderText="رقم التعريف" SortExpression="id" />
            <asp:BoundField DataField="start_date" HeaderText="تاريخ البداية" 
                SortExpression="start_date" DataFormatString="{0:d}" />
            <asp:BoundField DataField="start_time" HeaderText="وقت البداية" 
                SortExpression="start_time" />
            <asp:BoundField DataField="end_date" HeaderText="تاريخ النهاية" 
                SortExpression="end_date" DataFormatString="{0:d}" />
            <asp:BoundField DataField="end_time" HeaderText="وقت النهاية" 
                SortExpression="end_time" />
            <asp:BoundField DataField="penal_clause" HeaderText="قيمة الشرط الجزائي" 
                SortExpression="penal_clause" />
        </Columns>
    </asp:GridView>
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        SelectCommand="SELECT auction_id, id, start_date, end_date, start_time, end_time, penal_clause FROM TB_Auction WHERE (id = @id)">
                        <SelectParameters>
                            <asp:QueryStringParameter Name="id" QueryStringField="car_id" Type="Int32" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                </div>
                <div class="col-lg-6 col-md-6" dir="rtl">
                    <div class="contact__form">
                        <div>
                            <div class="row" dir="rtl">
                                <div class="col-lg-6">
                                    
                                <h5 align="right"> تاريخ بداية المزاد</h5>
                          <asp:TextBox ID="TextBox_start_da" runat="server" class="form-control"  TextMode="Date"></asp:TextBox>
                          </div> 
                                <div class="col-lg-6">
                                       <h5 align="right"> وقت بداية المزاد</h5>     
                          <asp:TextBox ID="TextBox_start_ti" runat="server" class="form-control"  TextMode="Time"></asp:TextBox>
                                <br/>
                                </div>
                                      <div class="col-lg-6">
                                    
                                <h5 align="right"> تاريخ نهاية المزاد</h5>
                          <asp:TextBox ID="TextBox_end_da" runat="server" class="form-control"  TextMode="Date"></asp:TextBox>
                          </div> 
                                <div class="col-lg-6">
                                       <h5 align="right"> وقت نهاية المزاد</h5>     
                          <asp:TextBox ID="TextBox_end_ti" runat="server" class="form-control"  TextMode="Time"></asp:TextBox>
                                <br/>
                                </div>
                                    
                                <div class="col-lg-6">
                                       <h5 align="right"> قيمة الشرط الجزائي</h5>     
                          <asp:TextBox ID="TextBox_panel" runat="server" class="form-control"  TextMode="SingleLine"></asp:TextBox>
                                <br/>
                                </div> 
                                
                            </div>
                          <br />
                          
                         
                    <asp:Button ID="Button1" runat="server" class="site-btn" Text="نشر بيانات المزاد" />
                           <br />
                        <asp:Label ID="Label1" runat="server" Text="Label" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</asp:Content>

