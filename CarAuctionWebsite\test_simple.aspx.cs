using System;
using System.Drawing;
using System.Web.UI;
using CarAuctionWebsite.App_Code;

namespace CarAuctionWebsite
{
    public partial class test_simple : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lblClassResult.Text = "انقر على الزر لاختبار الكلاسات";
                lblDBResult.Text = "انقر على الزر لاختبار قاعدة البيانات";
                lblCustomerResult.Text = "انقر على الزر لإنشاء عميل تجريبي";
            }
        }

        protected void btnTestClasses_Click(object sender, EventArgs e)
        {
            try
            {
                // اختبار إنشاء كائنات من الكلاسات
                CarClass carClass = new CarClass();
                CustomerClass customerClass = new CustomerClass();
                UserClass userClass = new UserClass();
                AuctionClass auctionClass = new AuctionClass();
                PaymentClass paymentClass = new PaymentClass();
                PhotoClass photoClass = new PhotoClass();
                BankClass bankClass = new BankClass();

                lblClassResult.Text = "<span class='success'>✓ تم إنشاء جميع الكلاسات بنجاح!</span><br/>";
                lblClassResult.Text += "• CarClass ✓<br/>";
                lblClassResult.Text += "• CustomerClass ✓<br/>";
                lblClassResult.Text += "• UserClass ✓<br/>";
                lblClassResult.Text += "• AuctionClass ✓<br/>";
                lblClassResult.Text += "• PaymentClass ✓<br/>";
                lblClassResult.Text += "• PhotoClass ✓<br/>";
                lblClassResult.Text += "• BankClass ✓<br/>";
            }
            catch (Exception ex)
            {
                lblClassResult.Text = $"<span class='error'>✗ خطأ في الكلاسات: {ex.Message}</span>";
            }
        }

        protected void btnTestDB_Click(object sender, EventArgs e)
        {
            try
            {
                CustomerClass customerClass = new CustomerClass();
                // محاولة تسجيل دخول بمستخدم غير موجود (لاختبار الاتصال)
                var result = customerClass.Login("test_user_not_exists");
                
                lblDBResult.Text = "<span class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح!</span><br/>";
                lblDBResult.Text += $"عدد النتائج: {result.Rows.Count} (متوقع: 0)";
            }
            catch (Exception ex)
            {
                lblDBResult.Text = $"<span class='error'>✗ خطأ في قاعدة البيانات: {ex.Message}</span><br/>";
                lblDBResult.Text += "<strong>تأكد من:</strong><br/>";
                lblDBResult.Text += "• تشغيل SQL Server Express<br/>";
                lblDBResult.Text += "• وجود ملف قاعدة البيانات في App_Data<br/>";
                lblDBResult.Text += "• صحة Connection String في Web.config";
            }
        }

        protected void btnCreateCustomer_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtPhone.Text) || string.IsNullOrEmpty(txtName.Text) || string.IsNullOrEmpty(txtPassword.Text))
                {
                    lblCustomerResult.Text = "<span class='error'>يرجى ملء جميع الحقول</span>";
                    return;
                }

                CustomerClass customerClass = new CustomerClass();
                customerClass.CreateAccount(
                    txtPhone.Text,
                    "طرابلس", // مدينة افتراضية
                    "المركز", // منطقة افتراضية
                    "عنوان تجريبي", // عنوان افتراضي
                    "نقطة تجريبية", // نقطة افتراضية
                    txtName.Text,
                    "بنك تجريبي", // بنك افتراضي
                    "فرع تجريبي", // فرع افتراضي
                    "حساب شخصي", // نوع افتراضي
                    txtPassword.Text
                );

                lblCustomerResult.Text = "<span class='success'>✓ تم إنشاء العميل بنجاح!</span><br/>";
                lblCustomerResult.Text += $"الهاتف: {txtPhone.Text}<br/>";
                lblCustomerResult.Text += $"الاسم: {txtName.Text}<br/>";
                lblCustomerResult.Text += "يمكنك الآن استخدام هذه البيانات لتسجيل الدخول";

                // مسح الحقول
                txtPhone.Text = "";
                txtName.Text = "";
                txtPassword.Text = "";
            }
            catch (Exception ex)
            {
                lblCustomerResult.Text = $"<span class='error'>✗ خطأ في إنشاء العميل: {ex.Message}</span>";
            }
        }
    }
}
