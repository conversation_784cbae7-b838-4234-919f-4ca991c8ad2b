﻿Imports System.Data

Partial Class claint_page_Car_details
    Inherits System.Web.UI.Page
    Dim obj_car As New Car_Class
    Dim obj_ac As New Auction_Class
    Dim obj_ph As New Photo_class
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim dt_id As DataTable = obj_ac.ID_auction(Request.QueryString("car_id"))
        If dt_id.Rows.Count > 0 Then
            Session("auction_number") = dt_id.Rows(0).Item("auction_id")
        Else
            Session("auction_number") = 0
            LinkButton1.Visible = False
        End If


        Dim dt As DataTable = obj_car.select_car(Request.QueryString("car_id"))
        If dt.Rows.Count > 0 Then
            Label_gas.Text = dt.Rows(0).Item("gas_type")
            Label_color.Text = dt.Rows(0).Item("color")
            Label_Cylinders.Text = dt.Rows(0).Item("Cylinders")
            Label_distance.Text = dt.Rows(0).Item("distance_travele")
            Label_engine.Text = dt.Rows(0).Item("Engine_Type")
            Label_key.Text = dt.Rows(0).Item("keys")
            Label_notes.Text = dt.Rows(0).Item("notes")
            Label_id.Text = dt.Rows(0).Item("id")
            Label_type.Text = dt.Rows(0).Item("car_type")
            Label_year.Text = dt.Rows(0).Item("year_made")
            Label1_damages.Text = dt.Rows(0).Item("damages")
            Label_start_v.Text = dt.Rows(0).Item("start_value").ToString
            Label_buy.Text = dt.Rows(0).Item("buy_now").ToString
            If dt.Rows(0).Item("state_pay") = False Then
                LinkButton2.Visible = True
            Else
                LinkButton2.Visible = False
            End If
        End If
        Dim dt_ac As DataTable = obj_ac.ID_auction(Request.QueryString("car_id"))
        If dt_ac.Rows.Count > 0 Then
            Dim dt2 As DataTable = obj_ac.select_auction(dt_ac.Rows(0).Item("auction_id"))

            If dt2.Rows.Count > 0 Then
                Session("auction_id") = dt2.Rows(0).Item("auction_id")
                TextBox_start_date.Text = dt2.Rows(0).Item("start_date")
                TextBox_end_y.Text = dt2.Rows(0).Item("end_date")
                TextBox_start_time.Text = dt2.Rows(0).Item("start_time").ToString
                TextBox_end_t.Text = dt2.Rows(0).Item("end_time").ToString
            End If
        End If

        Dim dt3 As DataTable = obj_ph.select_photo(Request.QueryString("car_id"))
        If dt3.Rows.Count > 0 Then
            Image_cover.ImageUrl = "~/Car_Photo/" + dt3.Rows(0).Item("Photo_name")
        End If
        Dim dt4 As DataTable = obj_ph.select_all_photo(Request.QueryString("car_id"))
        Dim c As Integer = 0
        If dt4.Rows.Count > 0 Then
            While c < dt4.Rows.Count
                If c = 0 Then
                    Image1.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 1 Then
                    Image2.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 2 Then
                    Image3.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 3 Then
                    Image4.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")
                End If
                If c = 4 Then
                    Image5.ImageUrl = "~/Car_Photo/" + dt4.Rows(c).Item("Photo_name")

                End If

                c = c + 1
            End While
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        Response.Redirect("~/claint_page/Payment_page.aspx?auction_id=" + Session("auction_id").ToString)
    End Sub

    Protected Sub LinkButton2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton2.Click
        Response.Redirect("~/claint_page/buy_page.aspx?id=" + Request.QueryString("car_id"))
    End Sub
End Class
