﻿Imports System.Data.SqlClient
Imports System.Data
Imports Microsoft.VisualBasic


Public Class bank_class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Sub sub_value(ByVal account_id, ByVal value_pay)
        Dim cmd As New SqlCommand("update TB_bank set totail=totail-@value_pay where account_id=@account_id", con)
        cmd.Parameters.AddWithValue("@account_id", account_id)
        cmd.Parameters.AddWithValue("@value_pay", value_pay)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Function check_account(ByVal account_id)
        Dim cmd As New SqlCommand("select totail from TB_bank where account_id=@account_id", con)
        cmd.Parameters.AddWithValue("@account_id", account_id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt
    End Function
End Class
