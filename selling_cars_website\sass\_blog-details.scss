/*---------------------
  Blog Hero
-----------------------*/
.blog-details-hero {
    padding-top: 80px;
    padding-bottom: 400px;
}

.blog__details__hero__text {
    text-align: center;

    .label {
        font-size: 15px;
        color: $primary-color;
        font-weight: 700;
        text-transform: uppercase;
    }

    h2 {
        font-size: 40px;
        color: $white-color;
        font-weight: 700;
        line-height: 55px;
        margin-top: 12px;
        margin-bottom: 20px;
    }

    ul {

        li {
            list-style: none;
            font-size: 15px;
            color: $white-color;
            display: inline-block;
            margin-right: 90px;
            
            span {
                display: inline-block;
                position: relative;

                &::after {
                    position: absolute;
                    right: -48px;
                    top: 3px;
                    height: 15px;
                    width: 1px;
                    background: $white-color;
                    content: '';
                }
            }

            &:last-child {
                margin-right: 0;

                span {
                    
                    &::after {
                        display: none;
                    }
                }
            }

            img {
                height: 34px;
                width: 34px;
                border-radius: 50%;
                margin-right: 6px;
            }

            i {
                color: $primary-color;
                margin-right: 2px;
            }
        }
    }
}

/*---------------------
  Blog Details
-----------------------*/
.blog-details {
    margin-top: -360px;
    padding-top: 0;
    overflow: hidden;
}

.blog__details__pic {
    margin-bottom: 95px;

    img {
        min-width: 100%;
    }
}

.blog__details__text {
    margin-bottom: 30px;

    p {
        font-size: 17px;
        line-height: 30px;

        &:first-child {
            margin-bottom: 25px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.blog__details__quote {
    padding: 0 60px;
    margin-bottom: 60px;

    p {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        line-height: 35px;
        position: relative;
        padding-left: 34px;
        margin-bottom: 0;

        &:before {
            position: absolute;
            left: 0;
            top: 0;
            height: 94px;
            width: 3px;
            background: $primary-color;
            content: '';
        }
    }
}

.blog__details__desc {
    margin-bottom: 42px;

    p {
        font-size: 17px;
        line-height: 30px;
        margin-bottom: 0;
    }
}

.blog__details__share {
    margin-right: -34px;
    overflow: hidden;
    margin-bottom: 40px;
}

.blog__details__share__item {
    display: inline-block;
    background: #37589C;
    border-radius: 2px;
    padding: 10px 10px 8px 16px;
    width: calc(25% - 34px);
    float: left;
    margin-right: 34px;
    margin-bottom: 20px;

    &.twitter {
        background: #54ADF0;
    }

    &.google {
        background: #DC4C39;
    }

    &.linkedin {
        background: #0179B6;
    }

    i {
        font-size: 18px;
        color: $white-color;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        padding-right: 15px;
        display: inline-block;
    }

    span {
        font-size: 15px;
        color: $white-color;
        font-weight: 600;
        display: inline-block;
        margin-left: 10px;
    }
}

.blog__details__author {
    overflow: hidden;
    margin-bottom: 60px;
}

.blog__details__author__pic {
    float: left;
    margin-right: 40px;

    img {
        height: 100px;
        width: 100px;
        border-radius: 50%;
    }
}

.blog__details__author__text {
    overflow: hidden;

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 12px;
    }

    p {
        font-size: 17px;
        line-height: 30px;
        margin-bottom: 0;
    }
}

.blog__details__btns {
    margin-bottom: 35px;
}

.blog__details__btns__item {
    display: block;
    border-radius: 2px;
    padding: 25px 20px 20px;
    margin-bottom: 20px;

    h6 {
        font-size: 15px;
        color: $white-color;
        font-weight: 700;
        margin-bottom: 26px;
    }

    ul {

        li {
            font-size: 13px;
            color: $white-color;
            display: inline-block;
            list-style: none;
            margin-right: 28px;
            position: relative;

            &:after {
                position: absolute;
                right: -19px;
                top: 4px;
                height: 13px;
                width: 1px;
                background: $white-color;
                content: '';
            }

            &:last-child {
                margin-right: 0;

                &:after {
                    display: none;
                }
            }
        }
    }
}

.blog__details__comment {
    margin-bottom: 60px;

    h4 {
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 35px;
    }
}

.blog__details__comment__item {
    overflow: hidden;
    margin-bottom: 30px;

    &.reply__comment {
        padding-top: 30px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        margin-left: 100px;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.blog__details__comment__item__pic {
    float: left;
    margin-right: 30px;
    
    img {
        height: 70px;
        width: 70px;
        border-radius: 50%;
    }
}

.blog__details__comment__item__text {
    overflow: hidden;

    h6 {
        font-size: 15px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 10px;
    }

    p {
        margin-bottom: 35px;
    }

    a {
        font-size: 15px;
        color: $heading-color;
        font-weight: 700;
        display: inline-block;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 2px;
        padding: 6px 20px 4px;
        margin-right: 6px;
        @include transition(all, .3s);

        &:hover {
            background: $primary-color;
            color: $white-color;
        }
    }
}

.blog__details__comment__form {

    h4 {
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 25px;
    }

    form {

        .input-list {
            margin-right: -20px;
            overflow: hidden;
        }

        .input-list-item {
            width: calc(33.33% - 20px);
            float: left;
            margin-right: 20px;
            margin-bottom: 20px;

            p {
                color: $heading-color;
                margin-bottom: 5px;
            }

            input {
                width: 100%;
                height: 46px;
                font-size: 15px;
                color: $heading-color;
                border-radius: 2px;
                padding-left: 20px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }
        }

        .input-desc {
            width: 100%;
            margin-bottom: 24px;

            p {
                color: $heading-color;
                margin-bottom: 5px;
            }

            textarea {
                width: 100%;
                height: 140px;
                font-size: 15px;
                color: $heading-color;
                border-radius: 2px;
                padding-left: 20px;
                padding-top: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                resize: none;
            }
        }
    }
}