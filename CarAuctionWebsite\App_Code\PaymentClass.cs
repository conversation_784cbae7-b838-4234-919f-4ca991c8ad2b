using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class PaymentClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void AddPayment(string phone, int auctionId, decimal maxValue, 
                              DateTime subDate, TimeSpan subTime, bool payState)
        {
            string query = @"INSERT INTO TB_payment (phone, auction_id, max_value, sub_date, sub_time, pay_state) 
                            VALUES (@phone, @auction_id, @max_value, @sub_date, @sub_time, @pay_state)";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@phone", phone);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            cmd.Parameters.AddWithValue("@max_value", maxValue);
            cmd.Parameters.AddWithValue("@sub_date", subDate);
            cmd.Parameters.AddWithValue("@sub_time", subTime);
            cmd.Parameters.AddWithValue("@pay_state", payState);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable CheckPayment(string phone, int auctionId)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_payment WHERE phone=@phone AND auction_id=@auction_id", con);
            cmd.Parameters.AddWithValue("@phone", phone);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public void UpdatePayment(string phone, int auctionId, decimal maxValue, 
                                 DateTime subDate, TimeSpan subTime)
        {
            string query = @"UPDATE TB_payment SET max_value=@max_value, sub_date=@sub_date, sub_time=@sub_time 
                            WHERE phone=@phone AND auction_id=@auction_id";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@phone", phone);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            cmd.Parameters.AddWithValue("@max_value", maxValue);
            cmd.Parameters.AddWithValue("@sub_date", subDate);
            cmd.Parameters.AddWithValue("@sub_time", subTime);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable CheckMaxValue(int auctionId)
        {
            SqlCommand cmd = new SqlCommand("SELECT max_value, phone FROM TB_payment WHERE auction_id=@auction_id", con);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            
            int c = 0;
            double max = 0;
            string ph = "";
            
            if (dt.Rows.Count > 0)
            {
                while (c < dt.Rows.Count)
                {
                    if (max < Convert.ToDouble(dt.Rows[c]["max_value"]))
                    {
                        max = Convert.ToDouble(dt.Rows[c]["max_value"]);
                        ph = dt.Rows[c]["phone"].ToString();
                    }
                    c++;
                }
            }
            
            DataTable dtNew = new DataTable();
            DataColumn c1 = new DataColumn("max_value", typeof(double));
            DataColumn c2 = new DataColumn("phone", typeof(string));
            dtNew.Columns.Add(c1);
            dtNew.Columns.Add(c2);
            dtNew.Rows.Add(max, ph);
            return dtNew;
        }

        public void CheckOut(string phone, int auctionId, string payWay, string shippingWay)
        {
            string query = @"UPDATE TB_payment SET pay_way=@pay_way, shipping_way=@shipping_way, pay_state='true' 
                            WHERE phone=@phone AND auction_id=@auction_id";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@phone", phone);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            cmd.Parameters.AddWithValue("@pay_way", payWay);
            cmd.Parameters.AddWithValue("@shipping_way", shippingWay);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }
    }
}
