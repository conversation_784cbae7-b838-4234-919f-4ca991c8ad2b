@echo off
echo ========================================
echo    Car Auction Website - مزاد السيارات
echo ========================================
echo.

echo Checking Visual Studio installation...
where devenv >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Visual Studio not found in PATH
    echo Please make sure Visual Studio 2022 is installed
    pause
    exit /b 1
)

echo Visual Studio found!
echo.

echo Opening project in Visual Studio 2022...
start "" "CarAuctionWebsite.sln"

echo.
echo Project opened in Visual Studio 2022
echo.
echo Next steps:
echo 1. Wait for Visual Studio to load completely
echo 2. Press F5 to run the project
echo 3. The website will open in your default browser
echo.
echo If you encounter any issues, please check:
echo - SQL Server Express is running
echo - .NET Framework 4.8 is installed
echo - All NuGet packages are restored
echo.
echo For detailed setup instructions, see SETUP_INSTRUCTIONS.md
echo.
pause
