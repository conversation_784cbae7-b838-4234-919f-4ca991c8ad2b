using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class CarClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void UpdateState(int id)
        {
            SqlCommand cmd = new SqlCommand("UPDATE TB_car SET state_pay='True' WHERE id=@id", con);
            cmd.Parameters.AddWithValue("@id", id);
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public void AddCar(string distanceTraveled, string damages, string cylinders, string color, 
                          string engineType, string carType, string yearMade, string gasType, 
                          string keys, string location, string notes, string addBy, 
                          decimal startValue, decimal buyNow)
        {
            string query = @"INSERT INTO TB_car (distance_travele, damages, Cylinders, color, Engine_Type, 
                            car_type, year_made, gas_type, keys, location, notes, state_pay, add_by, 
                            start_value, buy_now) 
                            VALUES (@distance_travele, @damages, @Cylinders, @color, @Engine_Type, 
                            @car_type, @year_made, @gas_type, @keys, @location, @notes, @state_pay, 
                            @add_by, @start_value, @buy_now)";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@distance_travele", distanceTraveled);
            cmd.Parameters.AddWithValue("@damages", damages);
            cmd.Parameters.AddWithValue("@Cylinders", cylinders);
            cmd.Parameters.AddWithValue("@color", color);
            cmd.Parameters.AddWithValue("@Engine_Type", engineType);
            cmd.Parameters.AddWithValue("@car_type", carType);
            cmd.Parameters.AddWithValue("@year_made", yearMade);
            cmd.Parameters.AddWithValue("@gas_type", gasType);
            cmd.Parameters.AddWithValue("@keys", keys);
            cmd.Parameters.AddWithValue("@location", location);
            cmd.Parameters.AddWithValue("@notes", notes);
            cmd.Parameters.AddWithValue("@state_pay", false);
            cmd.Parameters.AddWithValue("@add_by", addBy);
            cmd.Parameters.AddWithValue("@start_value", startValue);
            cmd.Parameters.AddWithValue("@buy_now", buyNow);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable SelectCar(int id)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_car WHERE id=@id", con);
            cmd.Parameters.AddWithValue("@id", id);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }
    }
}
