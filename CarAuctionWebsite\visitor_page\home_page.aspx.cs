using System;
using System.Data;
using System.Drawing;
using System.Web.UI;
using System.Web.UI.WebControls;
using CarAuctionWebsite.App_Code;

namespace CarAuctionWebsite.visitor_page
{
    public partial class home_page : System.Web.UI.Page
    {
        CustomerClass objC = new CustomerClass();
        UserClass objAd = new UserClass();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // عرض جميع السيارات بشكل افتراضي
                if (DataList1 != null)
                {
                    DataList1.Visible = true;
                    DataList1.DataBind();
                }
            }
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            // Validation for phone number
            if (string.IsNullOrEmpty(TextBox_phone.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال رقم الهاتف", Color.Red);
                TextBox_phone.Focus();
                return;
            }

            // Validation for city
            if (string.IsNullOrEmpty(TextBox_city.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال المدينه", Color.Red);
                TextBox_city.Focus();
                return;
            }

            // Validation for region
            if (string.IsNullOrEmpty(TextBox_region.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال المنطقه", Color.Red);
                TextBox_region.Focus();
                return;
            }

            // Validation for address
            if (string.IsNullOrEmpty(TextBox_address.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال العنوان", Color.Red);
                TextBox_address.Focus();
                return;
            }

            // Validation for point
            if (string.IsNullOrEmpty(TextBox_point.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال أقرب نقطه", Color.Red);
                TextBox_point.Focus();
                return;
            }

            // Validation for customer name
            if (string.IsNullOrEmpty(TextBox_customer.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال الاسم", Color.Red);
                TextBox_customer.Focus();
                return;
            }

            // Validation for bank
            if (string.IsNullOrEmpty(TextBox_bank.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال أسم البنك", Color.Red);
                TextBox_bank.Focus();
                return;
            }

            // Validation for branch
            if (string.IsNullOrEmpty(TextBox_branch.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال فرع البنك", Color.Red);
                TextBox_branch.Focus();
                return;
            }

            // Validation for password
            if (string.IsNullOrEmpty(TextBox_password.Text))
            {
                ShowMessage(Label1, "عفوا يجب إدخال كلمة السر", Color.Red);
                TextBox_password.Focus();
                return;
            }

            // Create account
            objC.CreateAccount(TextBox_phone.Text, TextBox_city.Text, TextBox_region.Text, 
                              TextBox_address.Text, TextBox_point.Text, TextBox_customer.Text, 
                              TextBox_bank.Text, TextBox_branch.Text, DropDownList1.Text, 
                              TextBox_password.Text);

            ShowMessage(Label1, "تم إنشاء الحساب بنجاح يمكنك تسجيل الدخول بصلاحية زبون", Color.Green);
            ClearForm();
        }

        protected void Button2_Click(object sender, EventArgs e)
        {
            DataTable dtAd = objAd.Login(TextBox_user.Text);
            
            if (dtAd.Rows.Count > 0)
            {
                if (dtAd.Rows[0]["User_password"].ToString() == TextBox_pass.Text)
                {
                    Session["admin_login"] = "admin";
                    // Response.Redirect("~/Admin_page/Car_Data.aspx"); // الصفحة غير موجودة حالياً
                    ShowMessage(Label2, "تم تسجيل الدخول كمدير بنجاح!", Color.Green);
                }
                else
                {
                    ShowMessage(Label2, "عفوا يوجد خطأ في كلمة المرور", Color.Red);
                    TextBox_pass.Focus();
                }
            }
            else
            {
                DataTable dt = objC.Login(TextBox_user.Text);
                if (dt.Rows.Count == 0)
                {
                    ShowMessage(Label2, "عفوا يوجد خطأ في اسم المستخدم", Color.Red);
                    TextBox_user.Focus();
                }
                else
                {
                    if (dt.Rows[0]["password"].ToString() == TextBox_pass.Text)
                    {
                        Session["customer_login"] = "customer";
                        Session["customer_phone"] = dt.Rows[0]["phone"];
                        // Response.Redirect("~/claint_page/Show_Cars.aspx"); // الصفحة غير موجودة حالياً
                        ShowMessage(Label2, "تم تسجيل الدخول كعميل بنجاح!", Color.Green);
                    }
                    else
                    {
                        ShowMessage(Label2, "عفوا يوجد خطأ في كلمة المرور", Color.Red);
                        TextBox_pass.Focus();
                    }
                }
            }
        }

        protected void LinkButton1_Click(object sender, EventArgs e)
        {
            // Handle search functionality based on radio button selection
            try
            {
                if (RadioButton_type != null && RadioButton_type.Checked)
                {
                    // البحث حسب النوع - يمكن إضافة منطق البحث هنا
                    // البحث حسب النوع - يمكن إضافة منطق البحث هنا
                    System.Diagnostics.Debug.WriteLine("البحث حسب النوع: " + (TextBox_type?.Text ?? ""));
                }
                else if (RadioButton_year != null && RadioButton_year.Checked)
                {
                    // البحث حسب السنة - يمكن إضافة منطق البحث هنا
                    // البحث حسب السنة - يمكن إضافة منطق البحث هنا
                    System.Diagnostics.Debug.WriteLine("البحث حسب السنة: " + (TextBox_year?.Text ?? ""));
                }
                else if (RadioButton_id != null && RadioButton_id.Checked)
                {
                    // البحث حسب الرقم - يمكن إضافة منطق البحث هنا
                    // البحث حسب الرقم - يمكن إضافة منطق البحث هنا
                    System.Diagnostics.Debug.WriteLine("البحث حسب الرقم: " + (TextBox_id?.Text ?? ""));
                }
                else
                {
                    // عرض جميع السيارات
                    if (DataList1 != null)
                    {
                        DataList1.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                // معالجة الأخطاء
                System.Diagnostics.Debug.WriteLine("خطأ في البحث: " + ex.Message);
            }
        }

        private void ShowMessage(Label label, string message, Color color)
        {
            if (label != null)
            {
                label.Visible = true;
                label.ForeColor = color;
                label.Text = message;
            }
        }

        private void ClearForm()
        {
            TextBox_phone.Text = "";
            TextBox_city.Text = "";
            TextBox_region.Text = "";
            TextBox_address.Text = "";
            TextBox_point.Text = "";
            TextBox_customer.Text = "";
            TextBox_bank.Text = "";
            TextBox_branch.Text = "";
            TextBox_password.Text = "";
        }
    }
}
