using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class PhotoClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void AddPhoto(string photoName, int id, bool state)
        {
            SqlCommand cmd = new SqlCommand("INSERT INTO TB_Photo (Photo_name, id, state) VALUES (@Photo_name, @id, @state)", con);
            cmd.Parameters.AddWithValue("@Photo_name", photoName);
            cmd.Parameters.AddWithValue("@id", id);
            cmd.Parameters.AddWithValue("@state", state);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable SelectPhoto(int id)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_Photo WHERE id=@id AND state='true'", con);
            cmd.Parameters.AddWithValue("@id", id);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public DataTable SelectAllPhoto(int id)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_Photo WHERE id=@id AND state='false'", con);
            cmd.Parameters.AddWithValue("@id", id);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }
    }
}
