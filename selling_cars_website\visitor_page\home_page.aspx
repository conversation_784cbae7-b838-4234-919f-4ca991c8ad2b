﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="home_page.aspx.cs" Inherits="CarAuctionWebsite.visitor_page.home_page" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
   <meta charset="UTF-8">
    <meta name="description" content="HVAC Template">
    <meta name="keywords" content="HVAC, unica, creative, html">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Home |Page</title>

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">

    <!-- Css Styles -->
    <link rel="stylesheet" href="../css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="../css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="../css/elegant-icons.css" type="text/css">
    <link rel="stylesheet" href="../css/nice-select.css" type="text/css">
    <link rel="stylesheet" href="../css/magnific-popup.css" type="text/css">
    <link rel="stylesheet" href="../css/jquery-ui.min.css" type="text/css">
    <link rel="stylesheet" href="../css/owl.carousel.min.css" type="text/css">
    <link rel="stylesheet" href="../css/slicknav.min.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css" type="text/css">
</head>
<body>
    <form id="form1" runat="server">
<!-- Page Preloder -->
    <div id="preloder">
        <div class="loader"></div>
    </div>

    <!-- Offcanvas Menu Begin -->
    <div class="offcanvas-menu-overlay"></div>
    <div class="offcanvas-menu-wrapper">
        <div class="offcanvas__widget">
            <a href="#"><i class="fa fa-cart-plus"></i></a>
            <a href="#" class="search-switch"><i class="fa fa-search"></i></a>
            <a href="#" class="primary-btn">Add Car</a>
        </div>
        <div class="offcanvas__logo">
            <a href="./index.html"><img src="img/logo.png" alt=""></a>
        </div>
        <div id="mobile-menu-wrap"></div>
        <ul class="offcanvas__widget__add">
            <li><i class="fa fa-clock-o"></i> Week day: 08:00 am to 18:00 pm</li>
            <li><i class="fa fa-envelope-o"></i> <EMAIL></li>
        </ul>
        <div class="offcanvas__phone__num">
            <i class="fa fa-phone"></i>
            <span>(+12) 345 678 910</span>
        </div>
        <div class="offcanvas__social">
            <a href="#"><i class="fa fa-facebook"></i></a>
            <a href="#"><i class="fa fa-twitter"></i></a>
            <a href="#"><i class="fa fa-google"></i></a>
            <a href="#"><i class="fa fa-instagram"></i></a>
        </div>
    </div>
    <!-- Offcanvas Menu End -->

    
    <!-- Header Section Begin -->
    <header class="header">
        <div class="header__top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-7">
                        <ul class="header__top__widget">
                            <li><i class="fa fa-clock-o"></i> المزاد اونلاين كل ايام الاسبوع عادا الجمعه</li>
                            <li><i class="fa fa-envelope-o"></i> <EMAIL></li>
                        </ul>
                    </div>
                    <div class="col-lg-5">
                        <div class="header__top__right">
                            <div class="header__top__phone">
                                <i class="fa fa-phone"></i>
                                <span>(+12) 345 678 910</span>
                            </div>
                            <div class="header__top__social">
                                <a href="#"><i class="fa fa-facebook"></i></a>
                                <a href="#"><i class="fa fa-twitter"></i></a>
                                <a href="#"><i class="fa fa-google"></i></a>
                                <a href="#"><i class="fa fa-instagram"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row" dir="rtl">
                <div class="col-lg-2">
                    <div class="header__logo">
                       
                    </div>
                </div>
                <div class="col-lg-10" >
                    <div class="header__nav">
                           
                        <nav class="header__menu">
                            <ul>
                              
                                <li class="active"><a href="home_page.aspx">الرئيسية</a></li>
                                <li><a href="#avalible car">المركبات المتوفرة</a></li>
                                <li><a href="#buy car">المركبات المباعة</a></li>
                                <li><a href="#services">خدمتنا</a></li>
                                
                            </ul>
                        </nav>
                        
                    </div>
                </div>
            </div>
            <div class="canvas__open">
                <span class="fa fa-bars"></span>
            </div>
        </div>
    </header>
    <!-- Header Section End -->

    <!-- Hero Section Begin -->
    <section class="hero spad set-bg" data-setbg="../img/hero-bg.jpg">
        <div class="container">
            <div class="row">
                <div class="col-lg-7">
                    <div class="hero__text">
                        <div class="hero__text__title">
                            <span>موقع خاص بمزاد السيارات</span>
                            <h2 >مزادنا</h2>
                        </div>
                       
                      
                    </div>
                </div>
                <div class="col-lg-5" dir="rtl">
                    <div class="hero__tab">
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#tabs-1" role="tab">إنشاء حساب</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#tabs-2" role="tab">تسجيل الدخول</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tabs-1" role="tabpanel">
                                <div class="hero__tab__form">
                                    <h2 align="right">بيانات إنشاء الحساب </h2>
                                    <div>
                                    <div class="row">
                                    <div class="col-lg-6">
                                    <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">رقم الهاتف</p>
                                                <asp:TextBox ID="TextBox_phone" runat="server" class="form-control" MaxLength="10"></asp:TextBox>
                                            </div>
                                             <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">المدينة</p>
                                                <asp:TextBox ID="TextBox_city" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                             <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">المنطقة</p>
                                                <asp:TextBox ID="TextBox_region" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                             <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">العنوان</p>
                                                <asp:TextBox ID="TextBox_address" runat="server" class="form-control" TextMode="MultiLine"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">أقرب نقطة</p>
                                                <asp:TextBox ID="TextBox_point" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                    </div>
                                    <div class="col-lg-6">
                                    <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">أسم الزبون</p>
                                                <asp:TextBox ID="TextBox_customer" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">كلمة السر</p>
                                                <asp:TextBox ID="TextBox_password" runat="server" class="form-control" MaxLength="12" TextMode="SingleLine"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">المصرف</p>
                                                <asp:TextBox ID="TextBox_bank" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">الفرع</p>
                                                <asp:TextBox ID="TextBox_branch" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">نوع الزبون</p>
                                                <asp:DropDownList ID="DropDownList1" runat="server" class="form-control">
                                                    <asp:ListItem>حساب شخصي</asp:ListItem>
                                                    <asp:ListItem>حساب شركة</asp:ListItem>
                                                </asp:DropDownList>
                                                
                                            </div>
                                    </div>
                                    </div>
                                    
                                    </div>
                                    <div dir="rtl">
                                        <div class="select-list">
                                            
                                          
                                        </div>
                                        <br />
                                        <br />
                                 <asp:Button ID="Button1" runat="server" Text="إرسال البيانات" class="site-btn"></asp:Button>
                                        <br />
                                <asp:Label ID="Label1" runat="server" Text="Label" Visible="False"></asp:Label>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="tabs-2" role="tabpanel">
                                <div class="hero__tab__form">
                                    <h2 dir="rtl">تسجيل الدخول</h2>
                                    <div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">اسم المستخدم</p>
                                                <asp:TextBox ID="TextBox_user" runat="server" class="form-control" MaxLength="50"></asp:TextBox>
                                            </div>
                                            <div class="select-list-item" dir="rtl">
                                                <p dir="ltr" align="right">كلمة المرور</p>
                                                <asp:TextBox ID="TextBox_pass" runat="server" class="form-control" MaxLength="12" TextMode="Password"></asp:TextBox>
                                            </div>

                                        

                                       <br />
                                        <br />
                                 <asp:Button ID="Button2" runat="server" Text="تسجيل الدخول" class="site-btn"></asp:Button>
                                  <br />
                              <asp:Label ID="Label2" runat="server" Text="Label" Visible="False"></asp:Label>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Hero Section End -->

    <!-- Services Section Begin -->
    <section class="services spad"id="services">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>خدماتنا</span>
                        <h2>ماذا يقدم موقعنا؟</h2>
                        <p>يقدم موقعنا امكانية شراء السيارات بطريقة اسهل واحدث واسرع من التقليديه</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="services__item">
                        
                        <h5>المزاد على السيارات</h5>
                        <p>يمكنك البحث على مختلف انواع السيارات والمشاركه بالمزاد على السياره المراد شرائها</p>
                       
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="services__item">
                        
                        <h5>الشراء الفوري للسيارات</h5>
                        <p>اذا اعجبتك سيارة ولا تريد فرصة فقدانها يمكنك الشراء الفوري للسياره بدون المشاركه في المزاد</p>
                        
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="services__item">
                        
                        <h5>متابعة المزاد اونلاين</h5>
                        <p>يمكنك متابعة المزادات الاخرى ورؤويتها مباشرة</p>
                       
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="services__item">
                      
                        <h5>عرض المركبات المباعة</h5>
                        <p>يمكنك التصفح عبر موقعنا وعرض السيارات التي تم بيعها من قبل </p>
                       
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Services Section End -->

    <!-- Feature Section Begin -->
    <section class="feature spad">
        <div class="container">
            <div class="row">
                <div class="col-lg-4">
                    <div class="feature__text">
                        <div class="section-title">
                            <div class="text-right">
                            <span>النصاىح</span>
                            </div>
                            <h2 class="text-right">العلامات المهمه في شاشة المركبة</h2>
                        </div>
                        <div class="feature__text__desc">
                            <p class="text-right">الاشياءالمهمه ويجب مراعاتها في كل مركبه</p>
                          
                        </div>
                      
                    </div>
                </div>
                <div class="col-lg-4 offset-lg-4">
                    <div class="row">
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/e.png" alt="" width="80" height="80">
                                </div>
                                <h6>المحرك</h6>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/oil.png" alt="" width="80" height="80">
                                </div>
                                <h6>زيت السياره</h6>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/hh.png" alt="" width="80" height="80">
                                </div>
                                <h6>التبريد</h6>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/sus.png" alt="" width="80" height="80">
                                </div>
                                <h6>نظام التعليق</h6>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/bat.png" alt="" width="80" height="80">
                                </div>
                                <h6>البطارية</h6>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-4 col-6">
                            <div class="feature__item">
                                <div class="feature__item__icon">
                                    <img src="../img/feature/break.png" alt="" width="80" height="80">
                                </div>
                                <h6>الفرامل</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Feature Section End -->

    <!-- Car Section Begin -->
    <section class="car spad"id="avalible car">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>تصفح المركبات</span>
                        <h2>المركبات المتوفرة</h2>
                    </div>
                    <table align="center" dir="rtl">
                    <tr>
                   <td> <asp:TextBox ID="TextBox_type" runat="server" class= "form-control" placeholder="أدخل نوع المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_type" runat="server" Text="البحث حسب النوع" GroupName="s"></asp:RadioButton></td> 
                   <td> <asp:TextBox ID="TextBox_year" runat="server" class= "form-control" placeholder="أدخل موديل المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_year" runat="server" Text="البحث حسب الموديل" GroupName="s"></asp:RadioButton></td> 
                   <td> <asp:TextBox ID="TextBox_id" runat="server" class= "form-control" placeholder="أدخل تعريف المركبة"></asp:TextBox> <asp:RadioButton ID="RadioButton_id" runat="server" Text="البحث حسب رقم التعريف" GroupName="s"></asp:RadioButton></td> 
                   <td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <asp:LinkButton ID="LinkButton1" runat="server" class="primary-btn"> عرض المركبات </asp:LinkButton> 
                    <h6>  &nbsp;&nbsp;&nbsp;</h6>
                    </td>
                    
                    
                    </tr>





                    </table>
                </div>
            </div>
 <asp:DataList ID="DataList1" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource1" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                </SelectParameters> 
            </asp:SqlDataSource>
            

            <!--  datalist for car by type -->
            
 <asp:DataList ID="DataList3" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource3" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource3" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.car_type = @car_type)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_type" DefaultValue="" Name="car_type" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>


            
            <!--  datalist for car by year -->
            
 <asp:DataList ID="DataList4" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource4" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource4" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.year_made = @year_made)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_year" DefaultValue="" Name="year_made" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>


               
            <!--  datalist for car by id -->
            
 <asp:DataList ID="DataList5" runat="server" DataKeyField="id" 
                DataSourceID="SqlDataSource5" RepeatColumns="3" RepeatDirection="Horizontal" 
                >
     <ItemTemplate>
         <div class="row car-filter">
                <div class="col-lg-11 col-md-11 col-sm-11 mix">
                    <div class="car__item">
                        
                            
                            <img src="../Car_Photo/<%# Eval("Photo_name") %>" alt="لا يوجد صورة">
                       
                        <div class="car__item__text">
                            <div class="car__item__text__inner">
                                <div class="label-date"><%# Eval("year_made") %></div>
                                <h5><a href="Car_details.aspx?car_id=<%# Eval("id ") %>"><%# Eval("car_type") %></a></h5>
                                <ul>
                                    <li><span><%# Eval("distance_travele") %> </span> mi</li>
                                    <li><%# Eval("gas_type") %> <i> <img src="../img/gass.png" width="20" height="20" /></i></li>
                                    
                                </ul>
                                 <span><%# Eval("location") %></span>  <i class="fa fa-map-marker "></i>
                            </div>
                            <div class="car__item__price">
                                <span class="car-option"><%# Eval("color") %> <i class="fa fa-paint-brush"> </i></span>
                                <h6><%# Eval("keys") %><span><i class ="fa fa-key "></i> </span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
     </ItemTemplate>
            </asp:DataList>
            <asp:SqlDataSource ID="SqlDataSource5" runat="server" 
                ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                
                
                
                
                
                
                
                SelectCommand="SELECT TB_Photo.Photo_name, TB_Photo.state, TB_car.id, TB_car.damages, TB_car.distance_travele, TB_car.Cylinders, TB_car.color, TB_car.Engine_Type, TB_car.year_made, TB_car.car_type, TB_car.gas_type, TB_car.keys, TB_car.location, TB_car.notes, TB_car.state_pay FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_car.id = @id)">
                <SelectParameters>
                    <asp:Parameter DefaultValue="True" Name="state" />
                    <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                    <asp:ControlParameter ControlID="TextBox_id" DefaultValue="" Name="id" 
                        PropertyName="Text" />
                </SelectParameters> 
            </asp:SqlDataSource>
            
            
        </div>
    </section>
    <!-- Car Section End -->

  

    <!-- Latest Blog Section Begin -->
    <section class="latest spad"id="buy car">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>تصفح المركبات</span>
                        <h2>المركبات المباعة عن طريق المزاد</h2>
                        
                    </div>
                </div>
            </div>
            <div class="row">

<asp:DataList ID="DataList2" runat="server" DataSourceID="SqlDataSource2" 
                    DataKeyField="Photo_name" RepeatDirection="Horizontal" RepeatColumns="3" 
                    Width="800px">
    <ItemTemplate>
     <div class="col-lg-12 col-md-12">
                    <div class="latest__blog__item">
                        <div class="latest__blog__item__pic set-bg" data-setbg="../Car_Photo/<%# Eval("Photo_name") %>">
                            <ul>
                                <li><%# Eval("year_made") %> <i class=" fa fa-calendar-check-o"> </i> </li>
                                <li><%# Eval("sub_date") %> <i class=" fa fa-calendar"> </i></li>
                                <li><%# Eval("max_value") %> <i class=" fa fa-money"> </i></li>
                            </ul>
                        </div>
                        <div class="latest__blog__item__text">
                            <h5><%# Eval("car_type") %></h5>
                            <p><%# Eval("damages") %>
                        </div>
                    </div>
                </div>
    </ItemTemplate>
                </asp:DataList>
                <asp:SqlDataSource ID="SqlDataSource2" runat="server" 
                    ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                    
                    SelectCommand="SELECT TB_Photo.Photo_name, TB_car.damages, TB_car.car_type, TB_car.year_made, TB_payment.sub_date, TB_Photo.state, TB_car.state_pay, TB_payment.pay_state, TB_payment.max_value, TB_Auction.penal_clause FROM TB_Auction INNER JOIN TB_car ON TB_Auction.id = TB_car.id INNER JOIN TB_payment ON TB_Auction.auction_id = TB_payment.auction_id INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_payment.pay_state = @pay_state) AND (TB_Auction.penal_clause != @penal_clause)">
                    <SelectParameters>
                        <asp:Parameter DefaultValue="True" Name="state" />
                        <asp:Parameter DefaultValue="True" Name="state_pay" />
                        <asp:Parameter DefaultValue="True" Name="pay_state" />
                        <asp:Parameter DefaultValue="0" Name="penal_clause" />
                    </SelectParameters>
                </asp:SqlDataSource>
                
                
            </div>


             <div class="row">
                <div class="col-lg-12">
                    <div class="section-title">
                        <span>تصفح المركبات</span>
                        <h2>المركبات المباعة عن طريق الشراء الفوري</h2>
                        
                    </div>
                </div>
            </div>
            <div class="row">

<asp:DataList ID="DataList6" runat="server" DataSourceID="SqlDataSource6" 
                    DataKeyField="Photo_name" RepeatDirection="Horizontal" RepeatColumns="3" 
                    Width="800px">
    <ItemTemplate>
     <div class="col-lg-12 col-md-12">
                    <div class="latest__blog__item">
                        <div class="latest__blog__item__pic set-bg" data-setbg="../Car_Photo/<%# Eval("Photo_name") %>">
                            <ul>
                                <li><%# Eval("year_made") %> <i class=" fa fa-calendar-check-o"> </i> </li>
                                <li><%# Eval("sub_date") %> <i class=" fa fa-calendar"> </i></li>
                                <li><%# Eval("buy_now")%> <i class=" fa fa-money"> </i></li>
                            </ul>
                        </div>
                        <div class="latest__blog__item__text">
                            <h5><%# Eval("car_type") %></h5>
                            <p><%# Eval("damages") %>
                        </div>
                    </div>
                </div>
    </ItemTemplate>
                </asp:DataList>
                <asp:SqlDataSource ID="SqlDataSource6" runat="server" 
                    ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                    
                    
                    SelectCommand="SELECT TB_Photo.Photo_name, TB_car.damages, TB_car.car_type, TB_car.year_made, TB_Photo.state, TB_car.state_pay, TB_car.buy_now, TB_Auction.penal_clause, TB_payment.sub_date FROM TB_car INNER JOIN TB_Photo ON TB_car.id = TB_Photo.Id INNER JOIN TB_Auction ON TB_car.id = TB_Auction.id INNER JOIN TB_payment ON TB_Auction.auction_id = TB_payment.auction_id WHERE (TB_Photo.state = @state) AND (TB_car.state_pay = @state_pay) AND (TB_Auction.penal_clause = @penal_clause)">
                    <SelectParameters>
                        <asp:Parameter DefaultValue="True" Name="state" />
                        <asp:Parameter DefaultValue="True" Name="state_pay" />
                        <asp:Parameter DefaultValue="0" Name="penal_clause" />
                    </SelectParameters>
                </asp:SqlDataSource>
                
                
            </div>
        </div>
    </section>
    <!-- Latest Blog Section End -->

   

    <!-- Footer Section Begin -->
    <footer class="footer set-bg" data-setbg="../img/footer-bg.jpg">
        <div class="container">
            <div class="footer__contact">
                <div class="row" dir="ltr">
                    <div class="col-lg-6 col-md-6" dir="ltr">
                        <div class="footer__contact__title">
                            <h2>تواصل معنا</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="footer__contact__option">
                            <div class="option__item"><i class="fa fa-phone"></i> (+218) 1234567</div>
                            <div class="option__item email"><i class="fa fa-envelope-o"></i> <EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-4">
                    <div class="footer__about">
                        <div class="footer__logo">
                           <%-- <a href="#"><img src="../img/footer-logo.png" alt=""></a>--%>
                        </div>
                        <p>لديك استفسار؟ يمكنك التواصل معنا عبر مواقعنا او عبر رقم الدعم الفني على(+218)1234567</p>
                        <div class="footer__social">
                            <a href="#" class="facebook"><i class="fa fa-facebook"></i></a>
                            <a href="#" class="twitter"><i class="fa fa-twitter"></i></a>
                            <a href="#" class="google"><i class="fa fa-google"></i></a>
                            <a href="#" class="skype"><i class="fa fa-skype"></i></a>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
            <div class="footer__copyright__text">
                <p>Copyright &copy;<script>                                       document.write(new Date().getFullYear());</script> All rights reserved |Eng Faraj Zuwawa <i class="" aria-hidden="true"></i> by <a href="https://www.facebook.com/me/" target="_blank">Personal account</a></p>
            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
        </div>
    </footer>
    <!-- Footer Section End -->

    <!-- Search Begin -->
    <div class="search-model">
        <div class="h-100 d-flex align-items-center justify-content-center">
            <div class="search-close-switch">+</div>
            <form class="search-model-form">
                <input type="text" id="search-input" placeholder="Search here.....">
            </form>
        </div>
    </div>
    <!-- Search End -->

    <!-- Js Plugins -->
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/jquery.nice-select.min.js"></script>
    <script src="../js/jquery-ui.min.js"></script>
    <script src="../js/jquery.magnific-popup.min.js"></script>
    <script src="../js/mixitup.min.js"></script>
    <script src="../js/jquery.slicknav.js"></script>
    <script src="../js/owl.carousel.min.js"></script>
    <script src="../js/main.js"></script>
    </form>
</body>
</html>
