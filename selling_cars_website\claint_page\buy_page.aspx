﻿ <%@ Page Title="" Language="VB" MasterPageFile="~/claint_page/Customer_Master.master" AutoEventWireup="false" CodeFile="buy_page.aspx.vb" Inherits="claint_page_buy_page" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="container">
            <div class="row">
            <div class="col-lg-2 col-md-2" dir="rtl">
                    
                </div>
                <div class="col-lg-8 col-md-8" dir="rtl">
                  <div class="contact__form">
                        <div>
                            <div class="row" dir="rtl">
                           
                                <div class="col-lg-6">
                                    
                                
                          <asp:TextBox ID="TextBox_shipping" runat="server" class="form-control" placeholder="ادخل طريقة الشحن" MaxLength="50"></asp:TextBox>
                          </div> 
                          <br /> 
                                <div class="col-lg-6">
                                    
                                
                          <asp:TextBox ID="TextBox_pay" runat="server" class="form-control" placeholder="ادخل طريقة الدفع" MaxLength="50" ></asp:TextBox>
                          <br />
                          </div> 
                            <br /> 
                           <div class="col-lg-6">
                                    
                                
                          <asp:TextBox ID="TextBox_account" runat="server" class="form-control" placeholder="ادخل رقم الحساب المصرفي" MaxLength="50" ></asp:TextBox>
                          <br />
                          </div> 
                           
                            </div>
                          <br />
                          
                         
                    <asp:Button ID="Button2" runat="server" class="site-btn" Text="تأكيد الدفع"  />
                           <br />
                        <asp:Label ID="Label2" runat="server" Text="Label" Visible="False"></asp:Label>
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-2" dir="rtl">
                    
                </div>
            </div>
        </div>
</asp:Content>

