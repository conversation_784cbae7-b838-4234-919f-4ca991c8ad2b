<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="test_simple.aspx.cs" Inherits="CarAuctionWebsite.test_simple" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>اختبار بسيط - Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>اختبار بسيط للمشروع</h1>
        
        <div class="test-section">
            <h3>1. اختبار الكلاسات:</h3>
            <asp:Button ID="btnTestClasses" runat="server" Text="اختبار الكلاسات" OnClick="btnTestClasses_Click" />
            <br /><br />
            <asp:Label ID="lblClassResult" runat="server"></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>2. اختبار قاعدة البيانات:</h3>
            <asp:Button ID="btnTestDB" runat="server" Text="اختبار قاعدة البيانات" OnClick="btnTestDB_Click" />
            <br /><br />
            <asp:Label ID="lblDBResult" runat="server"></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>3. اختبار إنشاء عميل:</h3>
            <table>
                <tr>
                    <td>الهاتف:</td>
                    <td><asp:TextBox ID="txtPhone" runat="server" Text="0912345678"></asp:TextBox></td>
                </tr>
                <tr>
                    <td>الاسم:</td>
                    <td><asp:TextBox ID="txtName" runat="server" Text="اختبار العميل"></asp:TextBox></td>
                </tr>
                <tr>
                    <td>كلمة المرور:</td>
                    <td><asp:TextBox ID="txtPassword" runat="server" Text="123456"></asp:TextBox></td>
                </tr>
            </table>
            <br />
            <asp:Button ID="btnCreateCustomer" runat="server" Text="إنشاء عميل تجريبي" OnClick="btnCreateCustomer_Click" />
            <br /><br />
            <asp:Label ID="lblCustomerResult" runat="server"></asp:Label>
        </div>
        
        <div class="test-section">
            <h3>الروابط:</h3>
            <ul>
                <li><a href="Default.aspx">الصفحة الرئيسية</a></li>
                <li><a href="visitor_page/home_page.aspx">صفحة المزاد</a></li>
                <li><a href="test_db.aspx">اختبار قاعدة البيانات المفصل</a></li>
            </ul>
        </div>
    </form>
</body>
</html>
