using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class AuctionClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void AddAuction(int id, DateTime startDate, DateTime endDate, 
                              TimeSpan startTime, TimeSpan endTime, decimal penalClause)
        {
            string query = @"INSERT INTO TB_Auction (id, start_date, end_date, start_time, end_time, penal_clause) 
                            VALUES (@id, @start_date, @end_date, @start_time, @end_time, @penal_clause)";

            SqlCommand cmd = new SqlCommand(query, con);
            cmd.Parameters.AddWithValue("@id", id);
            cmd.Parameters.AddWithValue("@start_date", startDate);
            cmd.Parameters.AddWithValue("@end_date", endDate);
            cmd.Parameters.AddWithValue("@start_time", startTime);
            cmd.Parameters.AddWithValue("@end_time", endTime);
            cmd.Parameters.AddWithValue("@penal_clause", penalClause);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable SelectAuction(int auctionId)
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_Auction WHERE auction_id=@auction_id", con);
            cmd.Parameters.AddWithValue("@auction_id", auctionId);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public DataTable SelectAllAuction()
        {
            SqlCommand cmd = new SqlCommand("SELECT * FROM TB_Auction WHERE end_date<@end_date", con);
            cmd.Parameters.AddWithValue("@end_date", DateTime.Today);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public DataTable GetAuctionId(int id)
        {
            SqlCommand cmd = new SqlCommand("SELECT auction_id FROM TB_Auction WHERE id=@id", con);
            cmd.Parameters.AddWithValue("@id", id);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }

        public int GetMaxAuctionId()
        {
            SqlCommand cmd = new SqlCommand("SELECT MAX(auction_id) FROM TB_Auction", con);
            int max = 0;
            con.Open();
            object result = cmd.ExecuteScalar();
            if (result != null && result != DBNull.Value)
            {
                max = Convert.ToInt32(result);
            }
            con.Close();
            return max;
        }
    }
}
