/*---------------------
  About
-----------------------*/
.about {
    padding-bottom: 70px;
}

.section-title {

    &.about-title {

        h2 {
            line-height: 55px;
        }

        p {
            font-size: 17px;
            line-height: 30px;
            color: #6A6B7C;
        }
    }
}

.about__feature {
    padding-bottom: 60px;
}

.about__feature__item {
    text-align: center;
    margin-bottom: 30px;
    padding: 0 20px;

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-top: 30px;
        margin-bottom: 15px;
    }

    p {
        margin-bottom: 0;
    }
}

.about__pic {
    margin-bottom: 50px;

    img {
        min-width: 100%;
        border-radius: 10px;
    }
}

.about__item {
    margin-bottom: 30px;

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 15px;
    }

    p {
        margin-bottom: 0;
    }
}

/*---------------------
  Call
-----------------------*/
.call__text {

    .section-title {
        text-align: left;
        margin-bottom: 35px;

        h2 {
            color: $white-color;
            margin-bottom: 12px;
        }

        p {
            color: $white-color;
        }
    }

    a {
        font-size: 15px;
        color: $white-color;
        font-weight: 700;
        text-transform: uppercase;
        padding: 6px 0;
        position: relative;

        &:after {
            position: absolute;
            left: 0;
            bottom: 0;
            height: 2px;
            width: 100%;
            background: $primary-color;
            content: '';
        }
    }
}

.call__form {

    input {
        width: 100%;
        height: 48px;
        color: #727171;
        font-size: 15px;
        padding-left: 20px;
        border: none;
        border-radius: 2px;
        margin-bottom: 25px;
    }

    .nice-select {
        float: none;
        width: 100%;
        height: 48px;
        border-radius: 2px;
        line-height: 48px;
        margin-bottom: 25px;

        span {
            font-size: 15px;
            color: $heading-color;
        }

        .list {
            width: 100%;
            margin-top: 0;
            border-radius: 0;
        }

        &:after {
            height: 10px;
            width: 10px;
            border-bottom: 2px solid $heading-color-2;
            border-right: 2px solid $heading-color-2;
            margin-top: 0;
            right: 22px;
            top: 35%;
        }
    }

    button {
        letter-spacing: 1px;
    }
}

/*---------------------
  Team
-----------------------*/
.team {
    padding-bottom: 70px;
}

.section-title {
    
    &.team-title {
        margin-bottom: 50px;
    }
}

.team__item {
    margin-bottom: 30px;
}

.team__item__pic {

    img {
        min-width: 100%;
        border-radius: 2px;
    }
}

.team__item__text {
    padding-top: 20px;
    text-align: center;

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 5px;
    }

    span {
        font-size: 15px;
        color: #727171;
    }
}

/*---------------------
  Testimonial
-----------------------*/
.testimonial {
    padding-top: 0;
}

.section-title {

    &.testimonial-title {
        margin-bottom: 40px;
        padding-top: 90px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }
}

.testimonial__slider {

    &.owl-carousel {

        .col-lg-6 {
            max-width: 100%;
        }

        .owl-stage-outer {
            padding-top: 30px;
            padding-bottom: 40px;
        }

        .owl-nav {

            button {
                height: 50px;
                width: 50px;
                border-radius: 50%;
                background: #f4f6f8;
                line-height: 50px;
                font-size: 30px;
                color: $heading-color;
                text-align: center;
                position: absolute;
                left: -85px;
                top: 50%;
                -webkit-transform: translateY(-40px);
                transform: translateY(-40px);

                &.owl-next {
                    left: auto;
                    right: -85px;
                }
            }
        }

        .owl-dots {
            text-align: center;

            button {
                height: 10px;
                width: 10px;
                background: #ebebeb;
                border-radius: 50%;
                display: inline-block;
                margin-right: 10px;

                &.active {
                    background: $primary-color;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}

.testimonial__item {
    box-shadow: 0px 6px 15px rgba(50, 15, 15, 0.05);
    padding: 40px 30px 65px 40px;
    border-radius: 5px;

    p {
        font-size: 20px;
        color: $heading-color;
        line-height: 32px;
        margin-bottom: 0;
    }
}

.testimonial__item__author {
    overflow: hidden;
    margin-bottom: 22px;
}

.testimonial__item__author__pic {
    float: left;
    margin-right: 30px;
}

.testimonial__item__author__text {
    overflow: hidden;
    padding-top: 10px;

    .rating {
        margin-bottom: 5px;

        i {
            font-size: 20px;
            color: #F9B944;
        }
    }

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;

        span {
            font-size: 15px;
            color: #727171;
            font-weight: 400;
        }
    }
}

/*---------------------
  Counter
-----------------------*/
.counter {
    position: relative;
    z-index: 1;
    padding-bottom: 60px;
    padding-top: 80px;

    &:after {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        content: '';
        z-index: -1;
    }
}

.counter__item {
    text-align: center;
    margin-bottom: 30px;

    h2 {
        font-size: 60px;
        font-weight: 700;
        color: $white-color;
        display: inline-block;
    }
    
    strong {
        font-size: 60px;
        font-weight: 700;
        color: $white-color;
        display: inline-block;
        line-height: 72px;
    }
    
    p {
        font-size: 20px;
        margin-bottom: 0;
        text-transform: uppercase;
        margin-top: 5px;
        font-weight: 700;
        color: $white-color;
    }
}

/*---------------------
  Clients
-----------------------*/
.clients {
    padding-bottom: 70px;
}

.section-title {

    &.client-title {
        margin-bottom: 45px;
    }
}

.client__item {
    border: 1px solid #ebebeb;
    margin-bottom: 30px;
    height: 110px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/*---------------------
  About
-----------------------*/