﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Auction_online.aspx.vb" Inherits="visitor_page_Auction_online" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
     <meta charset="UTF-8">
    <meta name="description" content="HVAC Template">
    <meta name="keywords" content="HVAC, unica, creative, html">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Auction |Online</title>

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">

    <!-- Css Styles -->
    <link rel="stylesheet" href="../css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="../css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="../css/elegant-icons.css" type="text/css">
    <link rel="stylesheet" href="../css/nice-select.css" type="text/css">
    <link rel="stylesheet" href="../css/magnific-popup.css" type="text/css">
    <link rel="stylesheet" href="../css/jquery-ui.min.css" type="text/css">
    <link rel="stylesheet" href="../css/owl.carousel.min.css" type="text/css">
    <link rel="stylesheet" href="../css/slicknav.min.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css" type="text/css">
</head>
<body>
   

    <form id="form1" runat="server"> 
    
    <!-- Breadcrumb End -->
    <div class="breadcrumb-option set-bg" data-setbg="../img/breadcrumb-bg.jpg">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <div class="breadcrumb__text">
                        <h2>المزاد اونلاين</h2>
                        <div class="breadcrumb__links">
                                             
        <a href ="../visitor_page/home_page.aspx" ><i class="fa fa-home "></i> الرجوع للصفحة الرئيسية </asp:LinkButton></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Breadcrumb Begin -->
     <!-- Page Preloder -->
    <div id="preloder">
        <div class="loader"></div>
    </div>

  

    <!-- Car Details Section Begin -->
    <section class="car-details spad">
        <div class="container">
            <div class="row" align="center" dir="rtl">
                <div class="col-lg-12" >
                <h2> مشاركات الزبائن</h2>
                <hr />
                <br />
                
               <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px" 
                        AllowPaging="True">
                   <Columns>
                       <asp:BoundField DataField="customer_name" HeaderText="الزبون" 
                           SortExpression="customer_name" />
                       <asp:BoundField DataField="max_value" HeaderText="القيمة" 
                           SortExpression="max_value" />
                       <asp:BoundField DataField="sub_date" HeaderText="التاريخ" 
                           SortExpression="sub_date" DataFormatString="{0:d}" />
                       <asp:BoundField DataField="sub_time" HeaderText="الوقت" 
                           SortExpression="sub_time" />
                   </Columns>
                    </asp:GridView>
                    
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        SelectCommand="SELECT TB_customer.customer_name, TB_payment.max_value, TB_payment.sub_date, TB_payment.sub_time, TB_payment.auction_id FROM TB_customer INNER JOIN TB_payment ON TB_customer.phone = TB_payment.phone WHERE (TB_payment.auction_id = @auction_id) ORDER BY TB_payment.max_value DESC">
                        <SelectParameters>
                            <asp:SessionParameter Name="auction_id" SessionField="auction_number" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                    
                </div>
                
            </div>
        </div>
    </section>
    <!-- Car Details Section End -->

     <!-- Footer Section Begin -->
    <footer class="footer set-bg" data-setbg="../img/footer-bg.jpg">
        <div class="container">
            <div class="footer__contact">
                <div class="row" dir="ltr">
                    <div class="col-lg-6 col-md-6" dir="ltr">
                        <div class="footer__contact__title">
                            <h2>تواصل معنا</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="footer__contact__option">
                            <div class="option__item"><i class="fa fa-phone"></i> (+218) 1234567</div>
                            <div class="option__item email"><i class="fa fa-envelope-o"></i> <EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-4">
                    <div class="footer__about">
                        <div class="footer__logo">
                            <a href="#"><img src="../img/footer-logo.png" alt=""></a>
                        </div>
                        <p>لديك استفسار؟ يمكنك التواصل معنا عبر مواقعنا او عبر رقم الدعم الفني على(+218)1234567</p>
                        <div class="footer__social">
                            <a href="#" class="facebook"><i class="fa fa-facebook"></i></a>
                            <a href="#" class="twitter"><i class="fa fa-twitter"></i></a>
                            <a href="#" class="google"><i class="fa fa-google"></i></a>
                            <a href="#" class="skype"><i class="fa fa-skype"></i></a>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
            <div class="footer__copyright__text">
                <p>Copyright &copy;<script>                                       document.write(new Date().getFullYear());</script> All rights reserved |Eng Faraj Zuwawa <i class="" aria-hidden="true"></i> by <a href="https://www.facebook.com/me/" target="_blank">Personal account</a></p>
            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
        </div>
    </footer>
    <!-- Footer Section End -->

    <!-- Search Begin -->
    <div class="search-model">
        <div class="h-100 d-flex align-items-center justify-content-center">
            <div class="search-close-switch">+</div>
            <form class="search-model-form">
                <input type="text" id="search-input" placeholder="Search here.....">
            </form>
        </div>
    </div>
    <!-- Search End -->

    <!-- Js Plugins -->
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/jquery.nice-select.min.js"></script>
    <script src="../js/jquery-ui.min.js"></script>
    <script src="../js/jquery.magnific-popup.min.js"></script>
    <script src="../js/mixitup.min.js"></script>
    <script src="../js/jquery.slicknav.js"></script>
    <script src="../js/owl.carousel.min.js"></script>
    <script src="../js/main.js"></script>
 
    </form>

</body>
</html>
