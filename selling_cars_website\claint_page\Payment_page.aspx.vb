﻿Imports System.Data

Partial Class claint_page_Payment_page
    Inherits System.Web.UI.Page
    Dim obj As New Payment_Class
    Dim obj_a As New Auction_Class
    Dim obj_b As New bank_class
    Dim obj_car As New Car_Class

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        Dim dt As DataTable = obj_a.select_auction(Request.QueryString("auction_id"))
        Dim dt_car As DataTable = obj_car.select_car(dt.Rows(0).Item("id"))
        If Date.Today() > dt.Rows(0).Item("end_date") Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "تم إيقاف المزاد  "
            Exit Sub
        Else
            If Date.Now.TimeOfDay() > dt.Rows(0).Item("end_time") Then
                Label1.Visible = True
                Label1.ForeColor = Drawing.Color.Red
                Label1.Text = "تم إيقاف المزاد  "
                Exit Sub
            End If
        End If
        If Convert.ToDouble(TextBox_max_value.Text) < dt_car.Rows(0).Item("start_value") Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "القيمة المقدمه للعطاء اقل من القيمة المحدده لبداية المزاد  "
            Exit Sub
        End If
        Dim dt_p As DataTable = obj.check_payment(Session("customer_phone"), Request.QueryString("auction_id"))
        If dt_p.Rows.Count = 0 Then
            obj.add_payment(Session("customer_phone"), Request.QueryString("auction_id"), Convert.ToDouble(TextBox_max_value.Text), Date.Today, Date.Now.TimeOfDay, False)
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Green
            Label1.Text = "تم ارسال بيانات العطاء بنجاح"
        Else
            obj.update_payment(Session("customer_phone"), Request.QueryString("auction_id"), Convert.ToDouble(TextBox_max_value.Text), Date.Today, Date.Now.TimeOfDay)
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Green
            Label1.Text = "تم تحديث بيانات العطاء بنجاح"
        End If

    End Sub

    Protected Sub Page_Init(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Init
        Dim dt As DataTable = obj_a.select_auction(Request.QueryString("auction_id"))
        Session("id") = dt.Rows(0).Item("id").ToString
        If Date.Today() > dt.Rows(0).Item("end_date") Then
            Dim dt_p As DataTable = obj.check_max_value(Request.QueryString("auction_id"))
            If dt_p.Rows.Count > 0 Then
                If dt_p.Rows(0).Item("phone") = Session("customer_phone") Then
                    Session("value") = dt_p.Rows(0).Item("max_value").ToString
                    Button2.Visible = True
                    TextBox_account.ReadOnly = False
                    TextBox_pay.ReadOnly = False
                    TextBox_shipping.ReadOnly = False
                Else
                    Button2.Visible = False
                    TextBox_account.ReadOnly = True
                    TextBox_pay.ReadOnly = True
                    TextBox_shipping.ReadOnly = True
                    Label2.Visible = True
                    Label2.ForeColor = Drawing.Color.Red
                    Label2.Text = dt_p.Rows(0).Item("max_value").ToString + "لم يتم قبول العطاء المقدم منك حيث انه اكبر قيمه مقدمه كانت  "
                End If

            End If
        Else
            If Date.Now.TimeOfDay() > dt.Rows(0).Item("end_time") Then
                Dim dt_p As DataTable = obj.check_max_value(Request.QueryString("auction_id"))
                If dt_p.Rows.Count > 0 Then
                    If dt_p.Rows(0).Item("phone") = Session("customer_phone") Then
                        Session("value") = dt_p.Rows(0).Item("max_value").ToString
                        Button2.Visible = True
                        TextBox_account.ReadOnly = False
                        TextBox_pay.ReadOnly = False
                        TextBox_shipping.ReadOnly = False
                    Else
                        Button2.Visible = False
                        TextBox_account.ReadOnly = True
                        TextBox_pay.ReadOnly = True
                        TextBox_shipping.ReadOnly = True
                        Label2.Visible = True
                        Label2.ForeColor = Drawing.Color.Red
                        Label2.Text = dt_p.Rows(0).Item("max_value").ToString + "لم يتم قبول العطاء المقدم منك حيث انه اكبر قيمه مقدمه كانت  "
                    End If

                End If
            End If
        End If

    End Sub

    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim dt As DataTable = obj_b.check_account(TextBox_account.Text)
        If dt.Rows.Count = 0 Then

            Label2.Visible = True
            Label2.ForeColor = Drawing.Color.Red
            Label2.Text = "يوجد خطأ في رقم الحساب المصرفي المرسل"
            Exit Sub
        Else
            If dt.Rows(0).Item("totail") < Session("value") Then
                Label2.Visible = True
                Label2.ForeColor = Drawing.Color.Red
                Label2.Text = "لا تتوفر القيمةالمراد دفعها في حسابك"
                Exit Sub
            Else
                obj_car.update_state(Session("id"))
                obj_b.sub_value(TextBox_account.Text, Session("value"))
                obj.check_out(Session("customer_phone"), Request.QueryString("auction_id"), TextBox_pay.Text, TextBox_shipping.Text)
                Label2.Visible = True
                Label2.ForeColor = Drawing.Color.Green
                Label2.Text = "تم تأكيد الشراء وخصم القيمة من الحساب المصرفي"
            End If
        End If


    End Sub
End Class
