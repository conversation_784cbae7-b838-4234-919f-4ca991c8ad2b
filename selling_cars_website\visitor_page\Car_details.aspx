﻿<%@ Page Language="VB" AutoEventWireup="false" CodeFile="Car_details.aspx.vb" Inherits="visitor_page_Car_details" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="description" content="HVAC Template">
    <meta name="keywords" content="HVAC, unica, creative, html">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Car |Details</title>

    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap" rel="stylesheet">

    <!-- Css Styles -->
    <link rel="stylesheet" href="../css/bootstrap.min.css" type="text/css">
    <link rel="stylesheet" href="../css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="../css/elegant-icons.css" type="text/css">
    <link rel="stylesheet" href="../css/nice-select.css" type="text/css">
    <link rel="stylesheet" href="../css/magnific-popup.css" type="text/css">
    <link rel="stylesheet" href="../css/jquery-ui.min.css" type="text/css">
    <link rel="stylesheet" href="../css/owl.carousel.min.css" type="text/css">
    <link rel="stylesheet" href="../css/slicknav.min.css" type="text/css">
    <link rel="stylesheet" href="../css/style.css" type="text/css">
</head>
 <body>

    <form id="form1" runat="server">
   <!-- Page Preloder -->
    <div id="preloder">
        <div class="loader"></div>
    </div>

  

    <!-- Car Details Section Begin -->
    <section class="car-details spad">
        <div class="container">
            <div class="row">
                <div class="col-lg-9">
                    <div class="car__details__pic">
                        <div class="car__details__pic__large">
                            
                          <asp:Image ID="Image_cover" runat="server" class="car-big-img"></asp:Image>
                        </div>
                        <div class="car-thumbs">
                            <div class="car-thumbs-track car__thumb__slider owl-carousel">
                                <div class="ct"><asp:Image ID="Image1" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image2" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image3" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image4" runat="server"></asp:Image></div>
                                <div class="ct"><asp:Image ID="Image5" runat="server"></asp:Image></div>
                            </div>
                        </div>
                    </div>
                    <div class="car__details__tab">
                        <ul class="nav nav-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#tabs-1" role="tab">الاضرار</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#tabs-2" role="tab">الملاحظات</a>
                            </li>
                            
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active" id="tabs-1" role="tabpanel">
                                <div class="car__details__tab__info">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12">
                                            <div class="car__details__tab__info__item">
                                            <p >
                                            
                                            
                                           <asp:Label ID="Label1_damages" runat="server" Text="Label"></asp:Label>
                                            
                                            </p>   
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                                -
                            </div>
                            <div class="tab-pane" id="tabs-2" role="tabpanel">
                                <div class="car__details__tab__info">
                                    <div class="row">
                                        <div class="col-lg-12 col-md-12">
                                            <div class="car__details__tab__info__item">
                                                <p>
                                                
                                      <asp:Label ID="Label_notes" runat="server" Text="Label"></asp:Label>
                                            
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="col-lg-3" dir="rtl" >
                    <div class="car__details__sidebar">
                        <div class="car__details__sidebar__model">
                            <ul>
                                <li><table><tr> <td> رقم التعريف</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_id" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> نوع المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_type" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> موديل المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_year" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> لون المركبة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_color" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                            </ul>
                            <h5 align="right">تاريخ المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_start_date" runat="server" class="form-control" ReadOnly="True"></asp:TextBox></p>      
                             <h5 align="right">توقيت المزاد</h5>
                            <p align="right"> <asp:TextBox ID="TextBox_start_time" runat="server" class="form-control" TextMode="Time" ReadOnly="True"></asp:TextBox></p>  
                        </div>
                        <div class="car__details__sidebar__payment">
                            <ul>
                               <li><table><tr> <td> نوع المحرك</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_engine" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> المفتاح</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_key" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                              <li><table><tr> <td> الاسطوانات</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_Cylinders" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> نوع الوقود</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_gas" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                               <li><table><tr> <td> المسافة المقطوعة</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td><td> <span><asp:Label ID="Label_distance" runat="server" Text="0000"></asp:Label></span></td></tr> </table> </li>
                            </ul>
                             <asp:LinkButton ID="LinkButton1" runat="server" class="primary-btn"> متابعة المزاد </asp:LinkButton>
                                             
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Car Details Section End -->

     <!-- Footer Section Begin -->
    <footer class="footer set-bg" data-setbg="../img/footer-bg.jpg">
        <div class="container">
            <div class="footer__contact">
                <div class="row" dir="ltr">
                    <div class="col-lg-6 col-md-6" dir="ltr">
                        <div class="footer__contact__title">
                            <h2>تواصل معنا</h2>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-6">
                        <div class="footer__contact__option">
                            <div class="option__item"><i class="fa fa-phone"></i> (+218) 1234567</div>
                            <div class="option__item email"><i class="fa fa-envelope-o"></i> <EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-4">
                    <div class="footer__about">
                        <div class="footer__logo">
                            <a href="#"><img src="../img/footer-logo.png" alt=""></a>
                        </div>
                        <p>لديك استفسار؟ يمكنك التواصل معنا عبر مواقعنا او عبر رقم الدعم الفني على(+218)1234567</p>
                        <div class="footer__social">
                            <a href="#" class="facebook"><i class="fa fa-facebook"></i></a>
                            <a href="#" class="twitter"><i class="fa fa-twitter"></i></a>
                            <a href="#" class="google"><i class="fa fa-google"></i></a>
                            <a href="#" class="skype"><i class="fa fa-skype"></i></a>
                        </div>
                    </div>
                </div>

            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
            <div class="footer__copyright__text">
                <p>Copyright &copy;<script>                                       document.write(new Date().getFullYear());</script> All rights reserved |Eng Faraj Zuwawa <i class="" aria-hidden="true"></i> by <a href="https://www.facebook.com/me/" target="_blank">Personal account</a></p>
            </div>
            <!-- Link back to Colorlib can't be removed. Template is licensed under CC BY 3.0. -->
        </div>
    </footer>
    <!-- Footer Section End -->

    <!-- Search Begin -->
    <div class="search-model">
        <div class="h-100 d-flex align-items-center justify-content-center">
            <div class="search-close-switch">+</div>
            <form class="search-model-form">
                <input type="text" id="search-input" placeholder="Search here.....">
            </form>
        </div>
    </div>
    <!-- Search End -->

    <!-- Js Plugins -->
    <script src="../js/jquery-3.3.1.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
    <script src="../js/jquery.nice-select.min.js"></script>
    <script src="../js/jquery-ui.min.js"></script>
    <script src="../js/jquery.magnific-popup.min.js"></script>
    <script src="../js/mixitup.min.js"></script>
    <script src="../js/jquery.slicknav.js"></script>
    <script src="../js/owl.carousel.min.js"></script>
    <script src="../js/main.js"></script>
 
    </form>
</body>
</html>
