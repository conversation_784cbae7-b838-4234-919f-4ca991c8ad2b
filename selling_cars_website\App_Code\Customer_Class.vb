﻿Imports System.Data.SqlClient
Imports System.Data
Imports Microsoft.VisualBasic

Public Class Customer_Class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Sub create_account(ByVal phone, ByVal city, ByVal region, ByVal address, ByVal near_point, ByVal customer_name, ByVal bank_name, ByVal branch, ByVal customer_type, ByVal password)
        Dim cmd As New SqlCommand("insert into TB_customer (phone,city, region, address, near_point, customer_name, bank_name, branch, customer_type, password)values(@phone,@city,@region,@address,@near_point,@customer_name,@bank_name,@branch,@customer_type,@password)", con)
        cmd.Parameters.AddWithValue("@phone", phone)
        cmd.Parameters.AddWithValue("@city", city)
        cmd.Parameters.AddWithValue("@region", region)
        cmd.Parameters.AddWithValue("@address", address)
        cmd.Parameters.AddWithValue("@near_point", near_point)
        cmd.Parameters.AddWithValue("@customer_name", customer_name)
        cmd.Parameters.AddWithValue("@bank_name", bank_name)
        cmd.Parameters.AddWithValue("@branch", branch)
        cmd.Parameters.AddWithValue("@customer_type", customer_type)
        cmd.Parameters.AddWithValue("@password", password)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Function login(ByVal customer_name)
        Dim cmd As New SqlCommand("select * from TB_customer where customer_name=@customer_name", con)
        cmd.Parameters.AddWithValue("@customer_name", customer_name)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt
    End Function
End Class
