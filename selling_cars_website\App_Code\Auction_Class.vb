﻿Imports System.Data.SqlClient
Imports System.Data
Imports Microsoft.VisualBasic


Public Class Auction_Class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Sub add_auction(ByVal id, ByVal start_date, ByVal end_date, ByVal start_time, ByVal end_time, ByVal penal_clause)
        Dim cmd As New SqlCommand("insert into TB_Auction (id, start_date, end_date, start_time, end_time,penal_clause)values(@id, @start_date, @end_date, @start_time, @end_time,  @penal_clause)", con)
        cmd.Parameters.AddWithValue("@id", id)
        cmd.Parameters.AddWithValue("@start_date", start_date)
        cmd.Parameters.AddWithValue("@end_date", end_date)
        cmd.Parameters.AddWithValue("@start_time", start_time)
        cmd.Parameters.AddWithValue("@end_time", end_time)
        cmd.Parameters.AddWithValue("@penal_clause", penal_clause)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub


    Function select_auction(ByVal auction_id)
        Dim cmd As New SqlCommand("select * from TB_Auction where auction_id=@auction_id", con)
        cmd.Parameters.AddWithValue("@auction_id", auction_id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt

    End Function


    Function select_all_auction()
        Dim cmd As New SqlCommand("select * from TB_Auction where end_date<@end_date", con)
        cmd.Parameters.AddWithValue("@end_date", Date.Today)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt

    End Function


    Function ID_auction(ByVal id)
        Dim cmd As New SqlCommand("select auction_id from TB_Auction where id=@id", con)
        cmd.Parameters.AddWithValue("@id", id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt

    End Function



    Function max_auction()
        Dim cmd As New SqlCommand("select MAX(auction_id) from TB_Auction", con)
        Dim max As Integer = 0
        con.Open()
        If IsNumeric(cmd.ExecuteScalar) = True Then
            max = cmd.ExecuteScalar()
        End If
        con.Close()
        Return max
    End Function
End Class
