﻿Imports System.Data

Partial Class claint_page_Photo_Car
    Inherits System.Web.UI.Page
    Dim obj_photo As New Photo_class
    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If FileUpload1.HasFile = True Then
            FileUpload1.SaveAs(Server.MapPath("~/Car_Photo/") + Date.Today.ToString("hhmmssddmmyyyy") + FileUpload1.FileName)
        Else
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "يجب تحميل صوره لرفعها"
            Exit Sub
        End If
        Dim dt As DataTable = obj_photo.select_photo(Request.QueryString("car_id"))
        If dt.Rows.Count = 0 Then
            obj_photo.add_photo(Date.Today.ToString("hhmmssddmmyyyy") + FileUpload1.FileName, Request.QueryString("car_id"), True)
        Else
            obj_photo.add_photo(Date.Today.ToString("hhmmssddmmyyyy") + FileUpload1.FileName, Request.QueryString("car_id"), False)
        End If
        Label1.Visible = True
        Label1.ForeColor = Drawing.Color.Green
        Label1.Text = "تم إرفاق الصوره لبيانات المركبه"
        GridView1.DataBind()
    End Sub
End Class
