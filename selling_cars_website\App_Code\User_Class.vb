﻿Imports System.Data.SqlClient
Imports System.Data
Imports Microsoft.VisualBasic

Public Class User_Class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Function login(ByVal user_na)
        Dim cmd As New SqlCommand("select * from TB_user where User_name=@User_name", con)
        cmd.Parameters.AddWithValue("@User_name", user_na)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt
    End Function
    Sub add_user(ByVal user_na, ByVal pass)
        Dim cmd As New SqlCommand("insert into TB_user (User_name,User_password)values(@User_name,@User_password) ", con)
        cmd.Parameters.AddWithValue("@User_name", user_na)
        cmd.Parameters.AddWithValue("@User_password", pass)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Sub update_user(ByVal user_na, ByVal pass)
        Dim cmd As New SqlCommand("update TB_user set User_password=@User_password where User_name=@User_name", con)
        cmd.Parameters.AddWithValue("@User_name", user_na)
        cmd.Parameters.AddWithValue("@User_password", pass)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Sub delete_user(ByVal user_na)
        Dim cmd As New SqlCommand("delete from TB_user where User_name=@User_name", con)
        cmd.Parameters.AddWithValue("@User_name", User_na)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
End Class
