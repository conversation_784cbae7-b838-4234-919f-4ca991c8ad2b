/*---------------------
  Car
-----------------------*/
.car {
    padding-bottom: 120px;

    .section-title {
        margin-bottom: 20px;

        h2 {
            margin-bottom: 0;
        }
    }
}

.filter__controls {
    text-align: center;
    margin-bottom: 40px;

    li {
        list-style: none;
        display: inline-block;
        font-size: 13px;
        text-transform: uppercase;
        color: #8d8d8d;
        font-weight: 700;
        margin-right: 50px;
        position: relative;
        padding: 4px 0;
        cursor: pointer;

        &.active {
            color: $heading-color-2;

            &:after {
                opacity: 1;
            }
        }

        &:after {
            position: absolute;
            left: 0;
            bottom: 0;
            height: 2px;
            width: 100%;
            background: $primary-color;
            content: '';
            opacity: 0;
        }

        &:last-child {
            margin-right: 0;
        }
    }
}

.car__filter__option {
    background: #f7f7f7;
    padding: 12px 30px;
    margin-bottom: 30px;

    .car__filter__option__item {

        &.car__filter__option__item--right {
            text-align: right;
        }

        h6 {
            font-size: 15px;
            color: $heading-color;
            font-weight: 700;
            display: inline-block;
            margin-right: 15px;
        }

        .nice-select {
            width: auto;
            font-size: 15px;
            color: $heading-color;
            padding-left: 20px;
            padding-right: 50px;
            border: 1px solid #ebebeb;
            height: 36px;
            background: $white-color;
            line-height: 36px;
            border-radius: 0;
            float: none;
            display: inline-block;

            .list {
                width: 100%;
                margin-top: 0;
                border-radius: 0;
            }

            &:after {
                border-bottom: 1.5px solid #AAAAB3;
                border-right: 1.5px solid #AAAAB3;
                height: 7px;
                right: 22px;
                width: 7px;
            }
        }
    }
}

.car__item {
    margin-bottom: 30px;
}

.car__item__pic__slider {

    img {
        border-radius: 2px 2px 0 0;
    }

    &.owl-carousel {

        .owl-dots {
            text-align: center;
            position: absolute;
            width: 100%;
            left: 0;
            bottom: 15px;
            
            button {
                height: 8px;
                width: 8px;
                background: #8d8d8d;
                border-radius: 50%;
                margin-right: 8px;
                display: inline-block;

                &.active {
                    background: $white-color;
                }

                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }
}

.car__item__text {
    border: 1px solid #ebebeb;
}

.car__item__text__inner {
    padding: 20px 0 14px 20px;

    .label-date {
        display: inline-block;
        font-size: 13px;
        color: $heading-color-2;
        font-weight: 700;
        padding: 2px 15px 1px;
        border: 1px solid #ebebeb;
        border-radius: 2px;
    }

    h5 {
        margin-top: 10px;
        margin-bottom: 14px;

        a {
            color: $heading-color;
            font-weight: 700;
        }
    }

    ul {

        li {
            list-style: none;
            font-size: 15px;
            color: #8d8d8d;
            font-weight: 700;
            position: relative;
            display: inline-block;
            margin-right: 40px;

            &:after {
                position: absolute;
                right: -23px;
                top: 3px;
                height: 15px;
                width: 2px;
                background: #8d8d8d;
                content: "";
            }

            span {
                color: $heading-color-2;
            }

            &:last-child {
                margin-right: 0;

                &:after {
                    display: none;
                }
            }
        }
    }
}

.car__item__price {
    position: relative;

    .car-option {
        font-size: 15px;
        color: $white-color;
        font-weight: 700;
        background: #4971FF;
        display: inline-block;
        padding: 12px 22px 10px;
        border-radius: 2px 0 0 2px;
        position: absolute;
        left: 0;
        top: 0;

        &.sale {
            background: $primary-color;
        }
    }

    h6 {
        font-size: 15px;
        color: $primary-color;
        font-weight: 700;
        border-top: 1px solid #ebebeb;
        padding-left: 120px;
        padding-top: 14px;
        padding-bottom: 11px;

        span {
            color: #727171;
            font-size: 13px;
            font-weight: 400;
        }
    }
}

.pagination__option {
    padding-top: 20px;

    a {
        display: inline-block;
        height: 50px;
        width: 50px;
        font-size: 18px;
        color: #727171;
        font-weight: 700;
        border: 1px solid #ebebeb;
        border-radius: 2px;
        line-height: 50px;
        text-align: center;
        margin-right: 6px;
        @include transition(all, .3s);

        &:hover,
        &.active {
            border-color: $primary-color;
            color: $heading-color;
        }

        &:last-child {
            margin-right: 0;
        }

        span {
            font-size: 18px;
            position: relative;
            top: 2px;
            font-weight: 700;
        }
    }
}

.car__sidebar {
    background: #f7f7f7;
    padding: 30px;
}

.car__search {
    margin-bottom: 40px;

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 15px;
    }

    form {
        position: relative;

        input {
            height: 46px;
            width: 100%;
            font-size: 15px;
            color: #727171;
            padding-left: 20px;
            border: 1px solid #ebebeb;
            background: $white-color;

            &::placeholder {
                color: #727171;
            }
        }

        button {
            font-size: 14px;
            color: #AAAAB3;
            position: absolute;
            right: 0;
            top: 0;
            background: transparent;
            border: none;
            height: 100%;
            padding: 0 15px;
        }
    }
}

.car__filter {

    h5 {
        font-size: 20px;
        color: $heading-color;
        font-weight: 700;
        margin-bottom: 15px;
    }

    form {

        .nice-select {
            width: 100%;
            font-size: 15px;
            color: #727171;
            padding-left: 20px;
            border: 1px solid #ebebeb;
            height: 46px;
            background: $white-color;
            line-height: 46px;
            border-radius: 0;
            margin-bottom: 10px;
            float: none;

            .list {
                width: 100%;
                margin-top: 0;
                border-radius: 0;
            }

            &:after {
                border-bottom: 1.5px solid #AAAAB3;
                border-right: 1.5px solid #AAAAB3;
                height: 7px;
                right: 22px;
                width: 7px;
            }
        }

        .filter-price {
            margin-bottom: 30px;
            padding-top: 8px;

            p {
                color: $heading-color-2;
                margin-bottom: 5px;
                margin-bottom: 18px;
            }

            .price-range-wrap {
                position: relative;

                .ui-widget-content {
                    height: 4px;
                    background: rgb(0, 0, 0, 0.1);
                    border: none;
                    border-radius: 1px;
                }

                .ui-slider-horizontal .ui-slider-handle {
                    top: -5px;
                    margin-left: -4px;
                }

                .ui-slider .ui-slider-handle {
                    width: 14px;
                    height: 14px;
                    background: $primary-color;
                    border-radius: 50%;
                    cursor: pointer;
                }

                .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
                    border: none;
                }

                .ui-slider .ui-slider-range {
                    background: $primary-color;
                }
            }

            .price-input {
                position: absolute;
                left: 42px;
                top: -44px;

                input {
                    font-size: 15px;
                    color: $heading-color;
                    font-weight: 700;
                    border: none;
                    width: 200px;
                    background: transparent;
                }
            }
        }

        .car__filter__btn {
            text-align: center;
        }
    }
}