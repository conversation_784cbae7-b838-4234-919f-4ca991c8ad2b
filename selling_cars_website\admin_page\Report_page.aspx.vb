﻿Imports System.Data

Partial Class admin_page_Report_page
    Inherits System.Web.UI.Page
    Dim obj_au As New Auction_Class
    Dim obj_pay As New Payment_Class
    Protected Sub Page_Init(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Init
        Dim dt_gridview As New DataTable

        Dim c1 = New DataColumn("id", GetType(Integer))
        Dim c2 = New DataColumn("penal_clause", GetType(Double))
        Dim c3 = New DataColumn("phone", GetType(String))
        Dim c4 = New DataColumn("max_value", GetType(Double))
        Dim c5 = New DataColumn("sub_date", GetType(Date))
        Dim c6 = New DataColumn("sub_time", GetType(TimeSpan))
        Dim c7 = New DataColumn("pay_way", GetType(String))
        Dim c8 = New DataColumn("shipping_way", GetType(String))
        dt_gridview.Columns.Add(c1)
        dt_gridview.Columns.Add(c2)
        dt_gridview.Columns.Add(c3)
        dt_gridview.Columns.Add(c4)
        dt_gridview.Columns.Add(c5)
        dt_gridview.Columns.Add(c6)
        dt_gridview.Columns.Add(c7)
        dt_gridview.Columns.Add(c8)
        Dim dt_ac As DataTable = obj_au.select_all_auction()
        Dim c As Integer = 0
        If dt_ac.Rows.Count > 0 Then
            While c < dt_ac.Rows.Count
                Dim dt_c As DataTable = obj_pay.check_customer_pay(dt_ac.Rows(c).Item("auction_id"))
                If dt_c.Rows.Count > 0 Then

                    dt_gridview.Rows.Add(dt_ac.Rows(c).Item("id"), dt_ac.Rows(c).Item("penal_clause"), dt_c.Rows(0).Item("phone"), dt_c.Rows(0).Item("max_value"), dt_c.Rows(c).Item("sub_date"), dt_c.Rows(0).Item("sub_time"), dt_c.Rows(0).Item("pay_way"), dt_c.Rows(0).Item("shipping_way"))
                End If

                c = c + 1
            End While
        End If
        GridView2.DataSource = dt_gridview
        GridView2.DataBind()
    End Sub
End Class
