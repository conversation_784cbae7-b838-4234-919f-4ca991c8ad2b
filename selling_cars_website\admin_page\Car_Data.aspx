﻿<%@ Page Title="" Language="VB" MasterPageFile="~/admin_page/Admin_Master.master" AutoEventWireup="false" CodeFile="Car_Data.aspx.vb" Inherits="admin_page_Car_Data" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="container">

            <div class="row">
            <div class="col-lg-2 col-md-2" dir="rtl">
                    
                </div>
                <div class="col-lg-8 col-md-8" dir="rtl">
                  
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" DataKeyNames="id" 
                        DataSourceID="SqlDataSource1" GridLines="None" Width="700px">
        <Columns>
            <asp:BoundField DataField="id" HeaderText="رقم  التعريف" InsertVisible="False" 
                ReadOnly="True" SortExpression="id" />
            <asp:BoundField DataField="year_made" HeaderText="الموديل" 
                SortExpression="year_made" />
            <asp:BoundField DataField="distance_travele" HeaderText="المسافة المقطوعة" 
                SortExpression="distance_travele" />
            <asp:BoundField DataField="car_type" HeaderText="نوع السياره" 
                SortExpression="car_type" />
            <asp:BoundField DataField="Engine_Type" HeaderText="نوع المحرك" 
                SortExpression="Engine_Type" />
            <asp:BoundField DataField="color" HeaderText="اللون" SortExpression="color" />
            <asp:BoundField DataField="gas_type" HeaderText="نوع الوقود" 
                SortExpression="gas_type" />
            <asp:BoundField DataField="location" HeaderText="الموقع" 
                SortExpression="location" />
            
            <asp:BoundField DataField="start_value" HeaderText="فيمة المزاد" 
                SortExpression="start_value" />
            <asp:BoundField DataField="buy_now" HeaderText="قيمة الشراء الفوري" 
                SortExpression="buy_now" />
            
            <asp:TemplateField>
                <ItemTemplate>
                    <asp:HyperLink ID="HyperLink2" runat="server" 
                        NavigateUrl='<%# Eval("id", "~/Admin_page/Payment_Date.aspx?car_id={0}") %>'>المزاد</asp:HyperLink>
                </ItemTemplate>
            </asp:TemplateField>
        </Columns>
    </asp:GridView>
                    <asp:SqlDataSource ID="SqlDataSource1" runat="server" 
                        ConnectionString="<%$ ConnectionStrings:CarConDB %>" 
                        
                        SelectCommand="SELECT id, distance_travele, damages, Cylinders, color, Engine_Type, car_type, year_made, gas_type, keys, location, notes, state_pay, add_by, start_value, buy_now FROM TB_car WHERE (state_pay = @state_pay)">
                        <SelectParameters>
                            <asp:Parameter DefaultValue="False" Name="state_pay" Type="Boolean" />
                        </SelectParameters>
                    </asp:SqlDataSource>
                </div>
                <div class="col-lg-2 col-md-2" dir="rtl">
                    
                </div>
            </div>
        </div>
</asp:Content>

