﻿Imports Microsoft.VisualBasic
Imports System.Data.SqlClient
Imports System.Data
Public Class Car_Class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)

    Sub update_state(ByVal id)
        Dim cmd As New SqlCommand("update  TB_car set state_pay='True' where id=@id", con)
        cmd.Parameters.AddWithValue("@id", id)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Sub add_car(ByVal distance_travele, ByVal damages, ByVal Cylinders, ByVal color, ByVal Engine_Type, ByVal car_type, ByVal year_made, ByVal gas_type, ByVal keys, ByVal location, ByVal notes, ByVal add_by, ByVal start_value, ByVal buy_now)
        Dim cmd As New SqlCommand("insert into TB_car (distance_travele,damages,Cylinders,color,Engine_Type,car_type, year_made, gas_type, keys,location, notes, state_pay , add_by , start_value ,buy_now )values(@distance_travele,@damages,@Cylinders,@color,@Engine_Type,@car_type, @year_made,@gas_type,@keys, @location, @notes, @state_pay , @add_by , @start_value , @buy_now)", con)
        cmd.Parameters.AddWithValue("@distance_travele", distance_travele)
        cmd.Parameters.AddWithValue("@damages", damages)
        cmd.Parameters.AddWithValue("@Cylinders", Cylinders)
        cmd.Parameters.AddWithValue("@color", color)
        cmd.Parameters.AddWithValue("@Engine_Type", Engine_Type)
        cmd.Parameters.AddWithValue("@car_type", car_type)
        cmd.Parameters.AddWithValue("@year_made", year_made)
        cmd.Parameters.AddWithValue("@gas_type", gas_type)
        cmd.Parameters.AddWithValue("@keys", keys)
        cmd.Parameters.AddWithValue("@location", location)
        cmd.Parameters.AddWithValue("@notes", notes)
        cmd.Parameters.AddWithValue("@state_pay", False)
        cmd.Parameters.AddWithValue("@add_by", add_by)
        cmd.Parameters.AddWithValue("@start_value", start_value)
        cmd.Parameters.AddWithValue("@buy_now", buy_now)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub
    Function select_car(ByVal id)
        Dim cmd As New SqlCommand("select * from TB_car where id=@id", con)
        cmd.Parameters.AddWithValue("@id", id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt

    End Function
End Class
