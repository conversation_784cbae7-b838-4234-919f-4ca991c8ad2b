/*---------------------
  Feature
-----------------------*/
.feature {
  background: #f7f7f7;
  position: relative;
  z-index: 1;
  padding-bottom: 55px;

  &:after {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 605px;
    height: 404px;
    background-image: url(../img/feature/car.png);
    content: '';
    -webkit-transform: translate(-300px, -202px);
    transform: translate(-300px, -202px);
    z-index: -1;
  }
}

.feature__text {

  .section-title {
    text-align: left;
    margin-bottom: 34px;

    h2 {
      margin-bottom: 0;
    }
  }
}

.feature__text__desc {
  margin-bottom: 35px;

  p {
    margin-bottom: 30px;
  }
}

.feature__text__btn {

  .primary-btn {
    margin-right: 16px;
  }

  .partner-btn {
    background: #222;
  }
}

.feature__item {
  text-align: center;
  float: right;
  margin-bottom: 45px;

  .feature__item__icon {
    height: 100px;
    width: 100px;
    background: $white-color;
    border-radius: 50%;
    line-height: 100px;
    text-align: center;
    margin-bottom: 20px!important;
    margin: 0 auto;
  }

  h6 {
    font-size: 15px;
    color: $heading-color;
    font-weight: 700;
  }
}

/*---------------------
  Choose Us
-----------------------*/
.chooseus {
  background: #f7f7f7;
  position: relative;
  height: 530px;
  padding-bottom: 85px;

  .row {
    position: relative;
    z-index: 1;
  }
}

.chooseus__text {
  position: relative;
  z-index: 1;

  .section-title {
    margin-bottom: 10px;
    text-align: left;
    
    h2 {
      margin-top: 0;
    }
  }

  ul {
    margin-bottom: 28px;

    li {
      list-style: none;
      font-size: 15px;
      color: #727171;
      line-height: 36px;

      i {
        color: $primary-color;
      }
    }
  }
}

.chooseus__video {
  height: 530px;
  width: calc(100% - 46%);
  position: absolute;
  right: 0;
  top: 0;

  img {
    height: 100%;
    width: 100%;
  }

  .play-btn {
    height: 60px;
    width: 60px;
    background: $primary-color;
    border-radius: 50%;
    font-size: 25px;
    color: #ffffff;
    line-height: 64px;
    text-align: center;
    display: inline-block;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-30px, -30px);
    transform: translate(-30px, -30px);
    z-index: 9;

    i {
      position: relative;
      left: 3px;
      top: -1px;
    }
  }
}

/*---------------------
  Cta
-----------------------*/
.cta {
  padding-bottom: 70px;
}

.cta__item {
  position: relative;
  z-index: 1;
  padding: 50px 34px 65px;
  border-radius: 2px;
  margin-bottom: 30px;

  &:before {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    content: '';
    z-index: -1;
    border-radius: 2px;
  }

  h4 {
    color: $white-color;
    font-weight: 700;
    margin-bottom: 6px;
  }

  p {
    color: $white-color;
    margin-bottom: 0;
  }
}