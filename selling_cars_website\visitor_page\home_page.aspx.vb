﻿Imports System.Data
Partial Class visitor_page_home_page
    Inherits System.Web.UI.Page
    Dim obj_c As New Customer_Class
    Dim obj_ad As New User_Class
    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button1.Click
        If TextBox_phone.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال رقم الهاتف"
            Exit Sub
            TextBox_phone.Focus()
        End If
        If TextBox_city.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال المدينه"
            TextBox_city.Focus()
            Exit Sub
        End If
        If TextBox_region.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال المنطقه"
            TextBox_region.Focus()
            Exit Sub
        End If
        If TextBox_address.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال العنوان"
            TextBox_address.Focus()
            Exit Sub
        End If
        If TextBox_point.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال أقرب نقطه"
            TextBox_point.Focus()
            Exit Sub
        End If
        If TextBox_customer.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال الاسم"
            TextBox_customer.Focus()
            Exit Sub
        End If
        If TextBox_bank.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال أسم البنك"
            TextBox_bank.Focus()
            Exit Sub
        End If
        If TextBox_branch.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال فرع البنك"
            TextBox_branch.Focus()
            Exit Sub
        End If
        If TextBox_password.Text = "" Then
            Label1.Visible = True
            Label1.ForeColor = Drawing.Color.Red
            Label1.Text = "عفوا يجب إدخال كلمة السر"
            TextBox_password.Focus()
            Exit Sub
        End If


        obj_c.create_account(TextBox_phone.Text, TextBox_city.Text, TextBox_region.Text, TextBox_address.Text, TextBox_point.Text, TextBox_customer.Text, TextBox_bank.Text, TextBox_branch.Text, DropDownList1.Text, TextBox_password.Text)
        Label1.Visible = True
        Label1.ForeColor = Drawing.Color.Green
        Label1.Text = "تم إنشاء الحساب بنجاح يمكنك تسجيل الدخول بصلاحية زبون"
        TextBox_phone.Text = ""
        TextBox_city.Text = ""
        TextBox_region.Text = ""
        TextBox_address.Text = ""
        TextBox_point.Text = ""
        TextBox_customer.Text = ""
        TextBox_bank.Text = ""
        TextBox_branch.Text = ""
        TextBox_password.Text = ""
    End Sub


    Protected Sub Button2_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Button2.Click
        Dim dt_ad As DataTable = obj_ad.login(TextBox_user.Text)
        If dt_ad.Rows.Count > 0 Then
            If dt_ad.Rows(0).Item("User_password") = TextBox_pass.Text Then
                Session("admin_login") = "admin"
                Response.Redirect("~/Admin_page/Car_Data.aspx")
            Else
                Label2.Visible = True
                Label2.ForeColor = Drawing.Color.Red
                Label2.Text = "عفوا يوجد خطأ في كلمة المرور"
                TextBox_pass.Focus()
            End If

        Else
            Dim dt As DataTable = obj_c.login(TextBox_user.Text)
            If dt.Rows.Count = 0 Then
                Label2.Visible = True
                Label2.ForeColor = Drawing.Color.Red
                Label2.Text = "عفوا يوجد خطأ في اسم المستخدم"
                TextBox_user.Focus()
            Else
                If dt.Rows(0).Item("password") = TextBox_pass.Text Then
                    Session("customer_login") = "customer"
                    Session("customer_phone") = dt.Rows(0).Item("phone")
                    Response.Redirect("~/claint_page/Show_Cars.aspx")
                Else
                    Label2.Visible = True
                    Label2.ForeColor = Drawing.Color.Red
                    Label2.Text = "عفوا يوجد خطأ في كلمة المرور"
                    TextBox_pass.Focus()
                End If
            End If
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If RadioButton_id.Checked = False And RadioButton_type.Checked = False And RadioButton_year.Checked = False Then
            DataList1.Visible = True
            DataList1.DataBind()
        Else
            DataList1.Visible = False
        End If
    End Sub

    Protected Sub LinkButton1_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LinkButton1.Click
        If RadioButton_type.Checked = True Then
            DataList3.Visible = True
            DataList3.DataBind()
        Else
            DataList3.Visible = False
        End If
        If RadioButton_year.Checked = True Then
            DataList4.Visible = True
            DataList4.DataBind()
        Else
            DataList4.Visible = False
        End If
        If RadioButton_id.Checked = True Then
            DataList5.Visible = True
            DataList5.DataBind()
        Else
            DataList5.Visible = False
        End If

    End Sub
End Class
