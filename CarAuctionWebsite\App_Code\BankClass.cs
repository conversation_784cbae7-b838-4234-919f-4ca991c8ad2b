using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace CarAuctionWebsite.App_Code
{
    public class BankClass
    {
        private SqlConnection con = new SqlConnection(ConfigurationManager.ConnectionStrings["CarConDB"].ConnectionString);

        public void SubtractValue(string accountId, decimal valuePay)
        {
            SqlCommand cmd = new SqlCommand("UPDATE TB_bank SET totail=totail-@value_pay WHERE account_id=@account_id", con);
            cmd.Parameters.AddWithValue("@account_id", accountId);
            cmd.Parameters.AddWithValue("@value_pay", valuePay);
            
            con.Open();
            cmd.ExecuteNonQuery();
            con.Close();
        }

        public DataTable CheckAccount(string accountId)
        {
            SqlCommand cmd = new SqlCommand("SELECT totail FROM TB_bank WHERE account_id=@account_id", con);
            cmd.Parameters.AddWithValue("@account_id", accountId);
            SqlDataAdapter adp = new SqlDataAdapter(cmd);
            DataTable dt = new DataTable();
            adp.Fill(dt);
            return dt;
        }
    }
}
