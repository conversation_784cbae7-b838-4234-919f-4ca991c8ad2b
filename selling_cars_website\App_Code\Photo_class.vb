﻿Imports Microsoft.VisualBasic
Imports System.Data.SqlClient
Imports System.Data


Public Class Photo_class
    Dim con As New SqlConnection(ConfigurationManager.ConnectionStrings("CarConDB").ConnectionString)
    Sub add_photo(ByVal Photo_name, ByVal id, ByVal state)
        Dim cmd As New SqlCommand("insert into TB_Photo (Photo_name, id , state) values (@Photo_name , @id , @state)", con)
        cmd.Parameters.AddWithValue("@Photo_name", Photo_name)
        cmd.Parameters.AddWithValue("@id", id)
        cmd.Parameters.AddWithValue("@state", state)
        con.Open()
        cmd.ExecuteNonQuery()
        con.Close()
    End Sub

    Function select_photo(ByVal id)
        Dim cmd As New SqlCommand("select * from TB_Photo where id=@id and state='true'", con)
        cmd.Parameters.AddWithValue("@id", id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt
    End Function


    Function select_all_photo(ByVal id)
        Dim cmd As New SqlCommand("select * from TB_Photo where id=@id and state='false'", con)
        cmd.Parameters.AddWithValue("@id", id)
        Dim adp As New SqlDataAdapter(cmd)
        Dim dt As New DataTable
        adp.Fill(dt)
        Return dt
    End Function


End Class
